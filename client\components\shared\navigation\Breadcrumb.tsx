'use client';

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils/cn';

export interface BreadcrumbItem {
  title: string;
  href?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  className?: string;
  itemClassName?: string;
  activeClassName?: string;
  maxItems?: number;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = (
    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  ),
  className,
  itemClassName,
  activeClassName,
  maxItems,
}) => {
  const displayItems = maxItems && items.length > maxItems
    ? [
        items[0],
        { title: '...' } as BreadcrumbItem,
        ...items.slice(-(maxItems - 2))
      ]
    : items;

  return (
    <nav className={cn('flex items-center space-x-2 text-sm', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {displayItems.map((item, index) => {
          if (!item) return null;

          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.title === '...';

          return (
            <li key={index} className="flex items-center space-x-2">
              {index > 0 && (
                <span className="flex-shrink-0">
                  {separator}
                </span>
              )}
              
              {isEllipsis ? (
                <span className="text-gray-500 dark:text-gray-400">...</span>
              ) : isLast ? (
                <span
                  className={cn(
                    'flex items-center space-x-1 text-gray-900 dark:text-white font-medium',
                    activeClassName,
                    itemClassName
                  )}
                  aria-current="page"
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.title}</span>
                </span>
              ) : item.href ? (
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors',
                    itemClassName
                  )}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.title}</span>
                </Link>
              ) : item.onClick ? (
                <button
                  onClick={item.onClick}
                  className={cn(
                    'flex items-center space-x-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors',
                    itemClassName
                  )}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.title}</span>
                </button>
              ) : (
                <span
                  className={cn(
                    'flex items-center space-x-1 text-gray-600 dark:text-gray-400',
                    itemClassName
                  )}
                >
                  {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                  <span>{item.title}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumb?: BreadcrumbItem[];
  extra?: React.ReactNode;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  breadcrumb,
  extra,
  className,
  titleClassName,
  subtitleClassName,
}) => {
  return (
    <div className={cn('mb-6', className)}>
      {breadcrumb && breadcrumb.length > 0 && (
        <div className="mb-4">
          <Breadcrumb items={breadcrumb} />
        </div>
      )}
      
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h1 className={cn(
            'text-2xl font-bold text-gray-900 dark:text-white sm:text-3xl',
            titleClassName
          )}>
            {title}
          </h1>
          {subtitle && (
            <p className={cn(
              'mt-2 text-sm text-gray-600 dark:text-gray-400',
              subtitleClassName
            )}>
              {subtitle}
            </p>
          )}
        </div>
        
        {extra && (
          <div className="flex-shrink-0 ml-4">
            {extra}
          </div>
        )}
      </div>
    </div>
  );
};

interface TabItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

interface TabsProps {
  items: TabItem[];
  activeKey: string;
  onChange: (key: string) => void;
  className?: string;
  tabClassName?: string;
  activeTabClassName?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'line' | 'card' | 'pill';
}

export const Tabs: React.FC<TabsProps> = ({
  items,
  activeKey,
  onChange,
  className,
  tabClassName,
  activeTabClassName,
  size = 'md',
  variant = 'line',
}) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  const variantClasses = {
    line: {
      container: 'border-b border-gray-200 dark:border-gray-700',
      tab: 'border-b-2 border-transparent hover:border-gray-300 dark:hover:border-gray-600',
      active: 'border-blue-500 text-blue-600 dark:text-blue-400',
      inactive: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white',
    },
    card: {
      container: 'bg-gray-100 dark:bg-gray-800 rounded-lg p-1',
      tab: 'rounded-md transition-all',
      active: 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm',
      inactive: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white',
    },
    pill: {
      container: 'space-x-1',
      tab: 'rounded-full transition-all',
      active: 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300',
      inactive: 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white',
    },
  };

  const currentVariant = variantClasses[variant];

  return (
    <div className={cn(currentVariant.container, className)}>
      <nav className="flex space-x-0" aria-label="Tabs">
        {items.map((item) => {
          const isActive = item.key === activeKey;
          
          return (
            <button
              key={item.key}
              onClick={() => !item.disabled && onChange(item.key)}
              disabled={item.disabled}
              className={cn(
                'flex items-center space-x-2 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                sizeClasses[size],
                currentVariant.tab,
                isActive 
                  ? cn(currentVariant.active, activeTabClassName)
                  : currentVariant.inactive,
                item.disabled && 'opacity-50 cursor-not-allowed',
                tabClassName
              )}
              aria-current={isActive ? 'page' : undefined}
            >
              {item.icon && (
                <span className="flex-shrink-0">
                  {item.icon}
                </span>
              )}
              <span>{item.label}</span>
              {item.badge && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full">
                  {item.badge}
                </span>
              )}
            </button>
          );
        })}
      </nav>
    </div>
  );
};

interface StepsProps {
  current: number;
  items: {
    title: string;
    description?: string;
    icon?: React.ReactNode;
  }[];
  direction?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  status?: 'wait' | 'process' | 'finish' | 'error';
  className?: string;
}

export const Steps: React.FC<StepsProps> = ({
  current,
  items,
  direction = 'horizontal',
  size = 'md',
  status = 'process',
  className,
}) => {
  const sizeClasses = {
    sm: {
      icon: 'w-6 h-6',
      title: 'text-sm',
      description: 'text-xs',
    },
    md: {
      icon: 'w-8 h-8',
      title: 'text-base',
      description: 'text-sm',
    },
    lg: {
      icon: 'w-10 h-10',
      title: 'text-lg',
      description: 'text-base',
    },
  };

  const getStepStatus = (index: number) => {
    if (index < current) return 'finish';
    if (index === current) return status;
    return 'wait';
  };

  const statusClasses = {
    wait: {
      icon: 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400',
      title: 'text-gray-600 dark:text-gray-400',
      description: 'text-gray-500 dark:text-gray-500',
    },
    process: {
      icon: 'bg-blue-600 text-white',
      title: 'text-blue-600 dark:text-blue-400',
      description: 'text-gray-600 dark:text-gray-400',
    },
    finish: {
      icon: 'bg-green-600 text-white',
      title: 'text-green-600 dark:text-green-400',
      description: 'text-gray-600 dark:text-gray-400',
    },
    error: {
      icon: 'bg-red-600 text-white',
      title: 'text-red-600 dark:text-red-400',
      description: 'text-gray-600 dark:text-gray-400',
    },
  };

  return (
    <div className={cn(
      direction === 'horizontal' ? 'flex items-center' : 'flex flex-col',
      className
    )}>
      {items.map((item, index) => {
        const stepStatus = getStepStatus(index);
        const isLast = index === items.length - 1;
        const currentStatusClasses = statusClasses[stepStatus];

        return (
          <div
            key={index}
            className={cn(
              'flex items-center',
              direction === 'horizontal' ? 'flex-row' : 'flex-col',
              !isLast && direction === 'vertical' && 'mb-8'
            )}
          >
            <div className={cn(
              'flex items-center',
              direction === 'horizontal' ? 'flex-col' : 'flex-row'
            )}>
              {/* Step Icon */}
              <div className={cn(
                'flex items-center justify-center rounded-full font-semibold',
                sizeClasses[size].icon,
                currentStatusClasses.icon
              )}>
                {item.icon ? (
                  item.icon
                ) : stepStatus === 'finish' ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : stepStatus === 'error' ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>

              {/* Step Content */}
              <div className={cn(
                direction === 'horizontal' ? 'mt-2 text-center' : 'ml-4'
              )}>
                <div className={cn(
                  'font-medium',
                  sizeClasses[size].title,
                  currentStatusClasses.title
                )}>
                  {item.title}
                </div>
                {item.description && (
                  <div className={cn(
                    'mt-1',
                    sizeClasses[size].description,
                    currentStatusClasses.description
                  )}>
                    {item.description}
                  </div>
                )}
              </div>
            </div>

            {/* Connector */}
            {!isLast && (
              <div className={cn(
                direction === 'horizontal' 
                  ? 'flex-1 h-px bg-gray-300 dark:bg-gray-600 mx-4'
                  : 'w-px h-8 bg-gray-300 dark:bg-gray-600 ml-4'
              )} />
            )}
          </div>
        );
      })}
    </div>
  );
};
