'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  <PERSON><PERSON>ser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiBriefcase,
  FiCalendar,
  FiStar,
  FiExternalLink,
  FiAward,
  FiTarget,
  FiTrendingUp,
  FiUsers,
  FiBook,
  FiCode,
  FiGlobe
} from 'react-icons/fi';
import { Employee, EmployeeDepartment, EmployeeStatus } from '@/lib/types/employee';
import { GlassCard } from '@/components/ui/glass-card';

interface EmployeeDetailViewProps {
  employee: Employee;
}

// Helper function to validate and format image URLs
const getValidImageUrl = (imageUrl: string | undefined): string | null => {
  if (!imageUrl || imageUrl.trim() === '') {
    return null;
  }

  // If it's already a full URL, return it
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    try {
      new URL(imageUrl);
      return imageUrl;
    } catch {
      return null;
    }
  }

  // If it's a relative path, construct the full URL
  if (imageUrl.startsWith('/') || imageUrl.startsWith('media/')) {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';
    const serverBaseUrl = baseUrl.replace('/api/v1', '');
    return `${serverBaseUrl}/${imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl}`;
  }

  // For other cases, try to construct a valid URL
  try {
    new URL(imageUrl);
    return imageUrl;
  } catch {
    // If it's not a valid URL, treat it as a relative path
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';
    const serverBaseUrl = baseUrl.replace('/api/v1', '');
    return `${serverBaseUrl}/${imageUrl}`;
  }
};

// Helper function to get status color
const getStatusColor = (status: EmployeeStatus): string => {
  const colors = {
    [EmployeeStatus.ACTIVE]: 'bg-green-500/20 border-green-500/30 text-green-400',
    [EmployeeStatus.INACTIVE]: 'bg-gray-500/20 border-gray-500/30 text-gray-400',
    [EmployeeStatus.TERMINATED]: 'bg-red-500/20 border-red-500/30 text-red-400',
    [EmployeeStatus.ON_LEAVE]: 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400',
    [EmployeeStatus.PROBATION]: 'bg-orange-500/20 border-orange-500/30 text-orange-400',
    [EmployeeStatus.NOTICE_PERIOD]: 'bg-purple-500/20 border-purple-500/30 text-purple-400'
  };
  return colors[status] || colors[EmployeeStatus.INACTIVE];
};

// Helper function to get department color
const getDepartmentColor = (department: EmployeeDepartment): string => {
  const colors = {
    [EmployeeDepartment.ENGINEERING]: 'bg-blue-500/20 border-blue-500/30 text-blue-400',
    [EmployeeDepartment.DESIGN]: 'bg-pink-500/20 border-pink-500/30 text-pink-400',
    [EmployeeDepartment.MARKETING]: 'bg-green-500/20 border-green-500/30 text-green-400',
    [EmployeeDepartment.HR]: 'bg-purple-500/20 border-purple-500/30 text-purple-400',
    [EmployeeDepartment.FINANCE]: 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400',
    [EmployeeDepartment.OPERATIONS]: 'bg-orange-500/20 border-orange-500/30 text-orange-400',
    [EmployeeDepartment.SALES]: 'bg-red-500/20 border-red-500/30 text-red-400',
    [EmployeeDepartment.PRODUCT]: 'bg-indigo-500/20 border-indigo-500/30 text-indigo-400',
    [EmployeeDepartment.QUALITY_ASSURANCE]: 'bg-teal-500/20 border-teal-500/30 text-teal-400',
    [EmployeeDepartment.CUSTOMER_SUPPORT]: 'bg-cyan-500/20 border-cyan-500/30 text-cyan-400',
    [EmployeeDepartment.LEGAL]: 'bg-gray-500/20 border-gray-500/30 text-gray-400',
    [EmployeeDepartment.OTHER]: 'bg-slate-500/20 border-slate-500/30 text-slate-400'
  };
  return colors[department] || colors[EmployeeDepartment.OTHER];
};

// Helper function to format department name
const formatDepartmentName = (department: EmployeeDepartment): string => {
  const names = {
    [EmployeeDepartment.ENGINEERING]: 'Engineering',
    [EmployeeDepartment.DESIGN]: 'Design',
    [EmployeeDepartment.MARKETING]: 'Marketing',
    [EmployeeDepartment.HR]: 'Human Resources',
    [EmployeeDepartment.FINANCE]: 'Finance',
    [EmployeeDepartment.OPERATIONS]: 'Operations',
    [EmployeeDepartment.SALES]: 'Sales',
    [EmployeeDepartment.PRODUCT]: 'Product',
    [EmployeeDepartment.QUALITY_ASSURANCE]: 'Quality Assurance',
    [EmployeeDepartment.CUSTOMER_SUPPORT]: 'Customer Support',
    [EmployeeDepartment.LEGAL]: 'Legal',
    [EmployeeDepartment.OTHER]: 'Other'
  };
  return names[department] || 'Other';
};

// Helper function to format status name
const formatStatusName = (status: EmployeeStatus): string => {
  const names = {
    [EmployeeStatus.ACTIVE]: 'Active',
    [EmployeeStatus.INACTIVE]: 'Inactive',
    [EmployeeStatus.TERMINATED]: 'Terminated',
    [EmployeeStatus.ON_LEAVE]: 'On Leave',
    [EmployeeStatus.PROBATION]: 'Probation',
    [EmployeeStatus.NOTICE_PERIOD]: 'Notice Period'
  };
  return names[status] || 'Unknown';
};

export const EmployeeDetailView: React.FC<EmployeeDetailViewProps> = ({ employee }) => {
  const imageUrl = getValidImageUrl(employee.profileImage) || getValidImageUrl(employee.avatar);
  const fullName = `${employee.firstName} ${employee.lastName}`;
  
  // Calculate tenure
  const hireDate = new Date(employee.hireDate);
  const now = new Date();
  const tenureYears = ((now.getTime() - hireDate.getTime()) / (1000 * 60 * 60 * 24 * 365)).toFixed(1);

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <GlassCard className="relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-transparent to-accent-purple/10" />
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-primary-500/5 to-transparent rounded-full -translate-y-32 translate-x-32" />
          
          <div className="relative p-8">
            <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
              {/* Profile Image */}
              <div className="flex-shrink-0 mb-6 lg:mb-0">
                <div className="relative">
                  {imageUrl ? (
                    <div className="w-32 h-32 relative">
                      <Image
                        src={imageUrl}
                        alt={fullName}
                        fill
                        className="object-cover rounded-2xl ring-4 ring-primary-500/20"
                        onError={() => {
                          console.error('Image failed to load:', imageUrl);
                        }}
                      />
                    </div>
                  ) : (
                    <div className="w-32 h-32 bg-gradient-to-br from-primary-500 to-accent-purple rounded-2xl flex items-center justify-center ring-4 ring-primary-500/20">
                      <FiUser className="w-16 h-16 text-white" />
                    </div>
                  )}
                  <div className={`absolute -bottom-2 -right-2 w-8 h-8 rounded-full border-4 border-dark-800 flex items-center justify-center ${
                    employee.status === EmployeeStatus.ACTIVE ? 'bg-green-500' : 'bg-gray-500'
                  }`}>
                    <div className="w-3 h-3 bg-white rounded-full" />
                  </div>
                </div>
              </div>

              {/* Basic Info */}
              <div className="flex-1 space-y-4">
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h1 className="text-3xl font-bold text-white">{fullName}</h1>
                    {employee.isFeatured && (
                      <div className="flex items-center space-x-1 px-3 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-full">
                        <FiStar className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium text-yellow-400">Featured</span>
                      </div>
                    )}
                  </div>
                  <p className="text-xl text-primary-300 font-medium">{employee.position}</p>
                  <p className="text-primary-400 font-mono">#{employee.employeeId}</p>
                </div>

                {/* Status and Department */}
                <div className="flex flex-wrap items-center gap-3">
                  <span className={`px-4 py-2 rounded-full text-sm font-semibold border ${getStatusColor(employee.status)}`}>
                    {formatStatusName(employee.status)}
                  </span>
                  <span className={`px-4 py-2 rounded-full text-sm font-semibold border ${getDepartmentColor(employee.department)}`}>
                    {formatDepartmentName(employee.department)}
                  </span>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{tenureYears}</div>
                    <div className="text-sm text-primary-400">Years</div>
                  </div>
                  {employee.performanceRating && (
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">{employee.performanceRating}/5</div>
                      <div className="text-sm text-primary-400">Performance</div>
                    </div>
                  )}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{employee.projects?.length || 0}</div>
                    <div className="text-sm text-primary-400">Projects</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{employee.trainings?.length || 0}</div>
                    <div className="text-sm text-primary-400">Trainings</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </GlassCard>
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Contact & Personal Info */}
        <div className="space-y-6">
          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <GlassCard className="p-6">
              <div className="flex items-center space-x-2 mb-4">
                <FiMail className="w-5 h-5 text-primary-400" />
                <h3 className="text-lg font-semibold text-white">Contact Information</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <FiMail className="w-4 h-4 text-primary-400" />
                    <span className="text-sm text-primary-300">Email</span>
                  </div>
                  <span className="text-sm text-white font-medium">{employee.email}</span>
                </div>

                {employee.phone && (
                  <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FiPhone className="w-4 h-4 text-primary-400" />
                      <span className="text-sm text-primary-300">Phone</span>
                    </div>
                    <span className="text-sm text-white font-medium">{employee.phone}</span>
                  </div>
                )}

                {(employee.address?.city || employee.city) && (
                  <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <FiMapPin className="w-4 h-4 text-primary-400" />
                      <span className="text-sm text-primary-300">Location</span>
                    </div>
                    <span className="text-sm text-white font-medium">
                      {employee.address ?
                        `${employee.address.city}, ${employee.address.country}` :
                        employee.city
                      }
                    </span>
                  </div>
                )}

                {employee.emergencyContact && (
                  <div className="p-3 bg-dark-800/30 rounded-lg">
                    <div className="text-sm text-primary-300 mb-2">Emergency Contact</div>
                    <div className="text-sm text-white font-medium">{employee.emergencyContact}</div>
                    {employee.emergencyPhone && (
                      <div className="text-sm text-primary-400 mt-1">{employee.emergencyPhone}</div>
                    )}
                  </div>
                )}
              </div>
            </GlassCard>
          </motion.div>

          {/* Personal Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <GlassCard className="p-6">
              <div className="flex items-center space-x-2 mb-4">
                <FiUser className="w-5 h-5 text-primary-400" />
                <h3 className="text-lg font-semibold text-white">Personal Information</h3>
              </div>

              <div className="space-y-3">
                {employee.birthDate && (
                  <div className="flex justify-between">
                    <span className="text-sm text-primary-400">Birth Date</span>
                    <span className="text-sm text-white font-medium">
                      {new Date(employee.birthDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {employee.gender && (
                  <div className="flex justify-between">
                    <span className="text-sm text-primary-400">Gender</span>
                    <span className="text-sm text-white font-medium">{employee.gender}</span>
                  </div>
                )}
                {employee.maritalStatus && (
                  <div className="flex justify-between">
                    <span className="text-sm text-primary-400">Marital Status</span>
                    <span className="text-sm text-white font-medium">{employee.maritalStatus}</span>
                  </div>
                )}
                {employee.nationalId && (
                  <div className="flex justify-between">
                    <span className="text-sm text-primary-400">National ID</span>
                    <span className="text-sm text-white font-medium font-mono">{employee.nationalId}</span>
                  </div>
                )}
              </div>
            </GlassCard>
          </motion.div>

          {/* External Links */}
          {(employee.linkedinProfile || employee.githubProfile || employee.portfolioUrl) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <FiGlobe className="w-5 h-5 text-primary-400" />
                  <h3 className="text-lg font-semibold text-white">External Links</h3>
                </div>

                <div className="space-y-3">
                  {employee.linkedinProfile && (
                    <a
                      href={employee.linkedinProfile}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg hover:bg-dark-700/50 transition-colors duration-200"
                    >
                      <span className="text-sm text-primary-300">LinkedIn</span>
                      <FiExternalLink className="w-4 h-4 text-primary-400" />
                    </a>
                  )}
                  {employee.githubProfile && (
                    <a
                      href={employee.githubProfile}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg hover:bg-dark-700/50 transition-colors duration-200"
                    >
                      <span className="text-sm text-primary-300">GitHub</span>
                      <FiExternalLink className="w-4 h-4 text-primary-400" />
                    </a>
                  )}
                  {employee.portfolioUrl && (
                    <a
                      href={employee.portfolioUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg hover:bg-dark-700/50 transition-colors duration-200"
                    >
                      <span className="text-sm text-primary-300">Portfolio</span>
                      <FiExternalLink className="w-4 h-4 text-primary-400" />
                    </a>
                  )}
                </div>
              </GlassCard>
            </motion.div>
          )}
        </div>

        {/* Right Column - Employment & Professional Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Employment Details */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <GlassCard className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <FiBriefcase className="w-5 h-5 text-primary-400" />
                <h3 className="text-lg font-semibold text-white">Employment Details</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <FiCalendar className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-blue-400 font-medium">Hire Date</span>
                    </div>
                    <div className="text-white font-semibold">{new Date(employee.hireDate).toLocaleDateString()}</div>
                  </div>

                  {employee.jobTitle && (
                    <div className="p-4 bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <FiTarget className="w-4 h-4 text-green-400" />
                        <span className="text-sm text-green-400 font-medium">Job Title</span>
                      </div>
                      <div className="text-white font-semibold">{employee.jobTitle}</div>
                    </div>
                  )}

                  {employee.employmentType && (
                    <div className="p-4 bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <FiBriefcase className="w-4 h-4 text-purple-400" />
                        <span className="text-sm text-purple-400 font-medium">Employment Type</span>
                      </div>
                      <div className="text-white font-semibold">{employee.employmentType.replace('_', ' ')}</div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {employee.salary && (
                    <div className="p-4 bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <FiTrendingUp className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-yellow-400 font-medium">Annual Salary</span>
                      </div>
                      <div className="text-white font-semibold text-xl">${employee.salary.toLocaleString()}</div>
                      {employee.salaryDetails?.currency && employee.salaryDetails.currency !== 'USD' && (
                        <div className="text-xs text-yellow-300 mt-1">({employee.salaryDetails.currency})</div>
                      )}
                    </div>
                  )}

                  {employee.performanceRating && (
                    <div className="p-4 bg-gradient-to-br from-orange-500/10 to-orange-600/5 border border-orange-500/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <FiAward className="w-4 h-4 text-orange-400" />
                        <span className="text-sm text-orange-400 font-medium">Performance Rating</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-white font-semibold text-xl">{employee.performanceRating}/5</div>
                        <div className="flex-1 bg-dark-700 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(employee.performanceRating / 5) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {employee.contractType && (
                    <div className="p-4 bg-gradient-to-br from-teal-500/10 to-teal-600/5 border border-teal-500/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <FiUsers className="w-4 h-4 text-teal-400" />
                        <span className="text-sm text-teal-400 font-medium">Contract Type</span>
                      </div>
                      <div className="text-white font-semibold">{employee.contractType.replace('_', ' ')}</div>
                    </div>
                  )}
                </div>
              </div>
            </GlassCard>
          </motion.div>

          {/* Bio Section */}
          {employee.bio && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <FiBook className="w-5 h-5 text-primary-400" />
                  <h3 className="text-lg font-semibold text-white">Bio</h3>
                </div>
                <p className="text-primary-300 leading-relaxed">{employee.bio}</p>
              </GlassCard>
            </motion.div>
          )}

          {/* Skills Section */}
          {employee.skills && employee.skills.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <FiCode className="w-5 h-5 text-primary-400" />
                  <h3 className="text-lg font-semibold text-white">Skills</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {employee.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="px-3 py-2 bg-gradient-to-r from-primary-500/20 to-accent-purple/20 border border-primary-500/30 text-primary-300 rounded-lg text-sm font-medium hover:from-primary-500/30 hover:to-accent-purple/30 transition-all duration-200"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          )}

          {/* Technologies Section */}
          {employee.technologies && employee.technologies.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center space-x-2 mb-6">
                  <FiCode className="w-5 h-5 text-primary-400" />
                  <h3 className="text-lg font-semibold text-white">Technologies</h3>
                </div>
                <div className="space-y-4">
                  {(employee.technologySkills || []).map((tech, index) => (
                    <div key={index} className="p-4 bg-dark-800/30 rounded-lg border border-dark-700/50">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-white font-medium">{tech.technology?.name || 'Unknown'}</span>
                        <span className="text-primary-400 text-sm font-semibold">{tech.proficiencyLevel}%</span>
                      </div>
                      <div className="w-full bg-dark-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary-500 to-accent-purple h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${tech.proficiencyLevel}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          )}

          {/* Projects Section */}
          {employee.projects && employee.projects.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <FiTarget className="w-5 h-5 text-primary-400" />
                    <h3 className="text-lg font-semibold text-white">Projects</h3>
                  </div>
                  <span className="px-3 py-1 bg-primary-500/20 border border-primary-500/30 text-primary-300 rounded-full text-sm font-medium">
                    {employee.projects.length} Projects
                  </span>
                </div>
                <div className="space-y-4">
                  {employee.projects.map((project, index) => (
                    <div key={index} className="p-4 bg-gradient-to-br from-dark-800/50 to-dark-700/30 rounded-lg border border-dark-600/50 hover:border-primary-500/30 transition-all duration-300">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="text-white font-semibold text-lg">{project.projectName}</h4>
                          <p className="text-primary-400 font-medium">{project.role}</p>
                        </div>
                      </div>
                      {project.description && (
                        <p className="text-primary-300 mb-3 leading-relaxed">{project.description}</p>
                      )}
                      <div className="flex items-center space-x-4 text-sm text-primary-400">
                        <div className="flex items-center space-x-1">
                          <FiCalendar className="w-4 h-4" />
                          <span>{new Date(project.startDate).toLocaleDateString()}</span>
                        </div>
                        {project.endDate && (
                          <>
                            <span>-</span>
                            <span>{new Date(project.endDate).toLocaleDateString()}</span>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          )}

          {/* Trainings & Certifications Section */}
          {employee.trainings && employee.trainings.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <FiBook className="w-5 h-5 text-primary-400" />
                    <h3 className="text-lg font-semibold text-white">Training & Certifications</h3>
                  </div>
                  <span className="px-3 py-1 bg-green-500/20 border border-green-500/30 text-green-300 rounded-full text-sm font-medium">
                    {employee.trainings.length} Completed
                  </span>
                </div>
                <div className="space-y-4">
                  {employee.trainings.map((training, index) => (
                    <div key={index} className="p-4 bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="text-white font-semibold">{training.title}</h4>
                          <p className="text-green-400 text-sm font-medium">{training.provider}</p>
                        </div>
                        {training.isVerified && (
                          <span className="px-2 py-1 bg-green-500/30 border border-green-500/50 text-green-300 rounded text-xs font-medium">
                            Verified
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-green-300">
                        <FiCalendar className="w-4 h-4" />
                        <span>Completed: {new Date(training.completionDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          )}

          {/* Achievements Section */}
          {employee.achievements && employee.achievements.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <FiAward className="w-5 h-5 text-primary-400" />
                    <h3 className="text-lg font-semibold text-white">Achievements</h3>
                  </div>
                  <span className="px-3 py-1 bg-yellow-500/20 border border-yellow-500/30 text-yellow-300 rounded-full text-sm font-medium">
                    {employee.achievements.length} Awards
                  </span>
                </div>
                <div className="space-y-4">
                  {employee.achievements.map((achievement, index) => (
                    <div key={index} className="p-4 bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="text-white font-semibold">{achievement.title}</h4>
                          <p className="text-yellow-300 mt-1 leading-relaxed">{achievement.description}</p>
                        </div>
                        <span className="px-2 py-1 bg-yellow-500/30 border border-yellow-500/50 text-yellow-300 rounded text-xs font-medium">
                          {achievement.category}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-yellow-300">
                        <FiCalendar className="w-4 h-4" />
                        <span>{new Date(achievement.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};
