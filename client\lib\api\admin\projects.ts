import apiClient from '../client';
import { Project, CreateProjectDto, UpdateProjectDto, ProjectFilters } from '@/lib/types/project';
import { ApiResponse, PaginatedResponse } from '@/lib/types/api';

export const adminProjectsApi = {
  // Get all projects with filters and pagination
  getAll: (params: ProjectFilters): Promise<ApiResponse<PaginatedResponse<Project>>> =>
    apiClient.get('/admin/projects', { params }),

  // Get project by ID
  getById: (id: string): Promise<ApiResponse<Project>> =>
    apiClient.get(`/admin/projects/${id}`),

  // Create new project
  create: (data: CreateProjectDto): Promise<ApiResponse<Project>> =>
    apiClient.post('/admin/projects', data),

  // Update project
  update: (id: string, data: UpdateProjectDto): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${id}`, data),

  // Delete project (soft delete)
  delete: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/projects/${id}`),

  // Bulk operations
  bulkDelete: (ids: string[]): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/projects/bulk-delete', { ids }),

  bulkUpdateStatus: (ids: string[], status: string): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/projects/bulk-update-status', { ids, status }),

  // Project members management
  addMember: (projectId: string, memberData: any): Promise<ApiResponse<Project>> =>
    apiClient.post(`/admin/projects/${projectId}/members`, memberData),

  removeMember: (projectId: string, userId: string): Promise<ApiResponse<Project>> =>
    apiClient.delete(`/admin/projects/${projectId}/members/${userId}`),

  updateMemberRole: (projectId: string, userId: string, role: string): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${projectId}/members/${userId}`, { role }),

  // Project technologies
  addTechnology: (projectId: string, technologyId: string): Promise<ApiResponse<Project>> =>
    apiClient.post(`/admin/projects/${projectId}/technologies`, { technologyId }),

  removeTechnology: (projectId: string, technologyId: string): Promise<ApiResponse<Project>> =>
    apiClient.delete(`/admin/projects/${projectId}/technologies/${technologyId}`),

  // Project images
  uploadImage: (projectId: string, file: File): Promise<ApiResponse<{ imageUrl: string }>> => {
    const formData = new FormData();
    formData.append('image', file);
    return apiClient.post(`/admin/projects/${projectId}/upload-image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  deleteImage: (projectId: string, imageUrl: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/projects/${projectId}/images`, { data: { imageUrl } }),

  // Project status management
  activate: (id: string): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${id}/activate`),

  deactivate: (id: string): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${id}/deactivate`),

  feature: (id: string): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${id}/feature`),

  unfeature: (id: string): Promise<ApiResponse<Project>> =>
    apiClient.patch(`/admin/projects/${id}/unfeature`),

  // Project analytics
  getStats: (): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/projects/stats'),

  getProjectAnalytics: (id: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/projects/${id}/analytics`),

  // Export projects
  export: (format: 'csv' | 'excel', filters?: ProjectFilters): Promise<Blob> =>
    apiClient.get('/admin/projects/export', { 
      params: { format, ...filters },
      responseType: 'blob'
    }),

  // Project templates
  getTemplates: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/admin/projects/templates'),

  createFromTemplate: (templateId: string, data: any): Promise<ApiResponse<Project>> =>
    apiClient.post('/admin/projects/from-template', { templateId, ...data }),

  // Project timeline
  getTimeline: (id: string): Promise<ApiResponse<any[]>> =>
    apiClient.get(`/admin/projects/${id}/timeline`),

  // Project tasks
  getTasks: (id: string): Promise<ApiResponse<any[]>> =>
    apiClient.get(`/admin/projects/${id}/tasks`),

  // Project reports
  getProgressReport: (id: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/projects/${id}/progress-report`),

  getTimeReport: (id: string, startDate?: Date, endDate?: Date): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/projects/${id}/time-report`, { 
      params: { startDate, endDate }
    }),

  // Project collaboration
  getCollaborators: (id: string): Promise<ApiResponse<any[]>> =>
    apiClient.get(`/admin/projects/${id}/collaborators`),

  inviteCollaborator: (id: string, email: string, role: string): Promise<ApiResponse<void>> =>
    apiClient.post(`/admin/projects/${id}/invite`, { email, role }),

  // Project backup and restore
  backup: (id: string): Promise<ApiResponse<{ backupUrl: string }>> =>
    apiClient.post(`/admin/projects/${id}/backup`),

  restore: (id: string, backupId: string): Promise<ApiResponse<Project>> =>
    apiClient.post(`/admin/projects/${id}/restore`, { backupId }),
};
