'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Control, FieldErrors, UseFormRegister } from 'react-hook-form';
import {
  FiUser,
  FiSearch,
  FiPlus,
  FiX,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCode,
  FiCheck,
  FiUserPlus,
  FiUsers,
  FiAlertCircle,
  FiBriefcase
} from 'react-icons/fi';
import {
  CreateEmployeeData,
  EmployeeDepartment,
  EmployeeStatus,
  EmploymentType,
  DEPARTMENT_OPTIONS,
  STATUS_OPTIONS,
  EMPLOYMENT_TYPE_OPTIONS,
  GENDER_OPTIONS,
  MARITAL_STATUS_OPTIONS
} from '@/lib/types/employee';
import { User } from '@/lib/types/auth';
import { Technology } from '@/lib/types/technology';
import { GlassCard } from '@/components/ui/glass-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';

interface UserSearchResult extends User {
  isExistingEmployee?: boolean;
}

interface TechnologySelection {
  technologyId: string;
  technology: Technology;
  proficiencyLevel: number;
}

// User Selection Step
interface UserSelectionStepProps {
  userSearchQuery: string;
  setUserSearchQuery: (query: string) => void;
  filteredUsers: UserSearchResult[];
  selectedUser: UserSearchResult | null;
  createNewUser: boolean;
  onUserSelect: (user: UserSearchResult) => void;
  onCreateNewUser: () => void;
  usersLoading: boolean;
}

export const UserSelectionStep: React.FC<UserSelectionStepProps> = ({
  userSearchQuery,
  setUserSearchQuery,
  filteredUsers,
  selectedUser,
  createNewUser,
  onUserSelect,
  onCreateNewUser,
  usersLoading
}) => (
  <GlassCard className="p-6">
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-white mb-2">User Account Selection</h3>
        <p className="text-primary-300">
          Search for an existing user to link to this employee, or create a new user account.
        </p>
      </div>

      {/* Search Input */}
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Search users by name or email..."
          value={userSearchQuery}
          onChange={(e) => setUserSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* Search Results */}
      {userSearchQuery && (
        <div className="space-y-2">
          <h4 className="text-white font-medium">Search Results</h4>
          {usersLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-primary-500/30 border-t-primary-500 rounded-full animate-spin" />
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredUsers.map((user) => (
                <motion.div
                  key={user._id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                    selectedUser?._id === user._id
                      ? 'bg-primary-500/20 border-primary-500'
                      : 'bg-dark-800/30 border-dark-700 hover:bg-dark-800/50 hover:border-primary-500/50'
                  }`}
                  onClick={() => onUserSelect(user)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-purple rounded-full flex items-center justify-center">
                        <FiUser className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-medium">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-primary-300 text-sm">{user.email}</div>
                        {user.phone && (
                          <div className="text-primary-400 text-sm">{user.phone}</div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        user.role.toLowerCase() === 'admin'
                          ? 'bg-red-500/20 text-red-400'
                          : user.role.toLowerCase() === 'employee'
                            ? 'bg-blue-500/20 text-blue-400'
                            : 'bg-gray-500/20 text-gray-400'
                      }`}>
                        {user.role.toUpperCase()}
                      </span>
                      {user.isExistingEmployee && (
                        <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium">
                          Has Employee Record
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-primary-400">
              No users found matching your search.
            </div>
          )}
        </div>
      )}

      {/* Selected User Display */}
      {selectedUser && (
        <div className="p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
          <div className="flex items-center space-x-3">
            <FiCheck className="w-5 h-5 text-green-400" />
            <div>
              <div className="text-white font-medium">Selected User</div>
              <div className="text-green-300">
                {selectedUser.firstName} {selectedUser.lastName} ({selectedUser.email})
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create New User Option */}
      <div className="border-t border-dark-700 pt-6">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-white font-medium">Create New User</h4>
            <p className="text-primary-300 text-sm">
              Create a new user account for this employee
            </p>
          </div>
          <EnhancedButton
            type="button"
            variant={createNewUser ? "primary" : "secondary"}
            onClick={onCreateNewUser}
            className="flex items-center space-x-2"
          >
            <FiUserPlus className="w-4 h-4" />
            <span>{createNewUser ? 'Creating New User' : 'Create New User'}</span>
          </EnhancedButton>
        </div>
        
        {createNewUser && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-4 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              <FiPlus className="w-5 h-5 text-blue-400" />
              <div>
                <div className="text-white font-medium">New User Mode</div>
                <div className="text-blue-300 text-sm">
                  A new user account will be created with the employee information you provide in the next step.
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  </GlassCard>
);

// Employee Details Step
interface EmployeeDetailsStepProps {
  register: UseFormRegister<CreateEmployeeData>;
  errors: FieldErrors<CreateEmployeeData>;
  control: Control<CreateEmployeeData>;
  selectedUser: UserSearchResult | null;
  createNewUser: boolean;
}

export const EmployeeDetailsStep: React.FC<EmployeeDetailsStepProps> = ({
  register,
  errors,
  control,
  selectedUser,
  createNewUser
}) => (
  <GlassCard className="p-6">
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-white mb-2">Employee Details</h3>
        <p className="text-primary-300">
          {selectedUser 
            ? `Complete the employee information for ${selectedUser.firstName} ${selectedUser.lastName}`
            : createNewUser
              ? 'Enter the employee information and user account details'
              : 'Enter the employee information'
          }
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiUser className="w-4 h-4" />
            <span>Basic Information</span>
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                First Name *
              </label>
              <input
                {...register('firstName', { required: 'First name is required' })}
                type="text"
                disabled={!!selectedUser}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter first name"
              />
              {errors.firstName && (
                <p className="text-red-400 text-sm mt-1">{errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Last Name *
              </label>
              <input
                {...register('lastName', { required: 'Last name is required' })}
                type="text"
                disabled={!!selectedUser}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter last name"
              />
              {errors.lastName && (
                <p className="text-red-400 text-sm mt-1">{errors.lastName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Email *
              </label>
              <input
                {...register('email', { 
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address'
                  }
                })}
                type="email"
                disabled={!!selectedUser}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter email address"
              />
              {errors.email && (
                <p className="text-red-400 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Phone
              </label>
              <input
                {...register('phone')}
                type="tel"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter phone number"
              />
            </div>

            {/* Password field - only show when creating new user */}
            {createNewUser && (
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Password *
                </label>
                <input
                  {...register('password', {
                    required: createNewUser ? 'Password is required for new users' : false,
                    minLength: {
                      value: 8,
                      message: 'Password must be at least 8 characters'
                    }
                  })}
                  type="password"
                  className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter password (min 8 characters)"
                />
                {errors.password && (
                  <p className="text-red-400 text-sm mt-1">{errors.password.message}</p>
                )}
                <p className="text-primary-400 text-xs mt-1">
                  Password will be used for the new user account
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Professional Information */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiBriefcase className="w-4 h-4" />
            <span>Professional Information</span>
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Position *
              </label>
              <input
                {...register('position', { required: 'Position is required' })}
                type="text"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter job position"
              />
              {errors.position && (
                <p className="text-red-400 text-sm mt-1">{errors.position.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Department *
              </label>
              <select
                {...register('department', { required: 'Department is required' })}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {DEPARTMENT_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value} className="bg-dark-800">
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.department && (
                <p className="text-red-400 text-sm mt-1">{errors.department.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Employment Type
              </label>
              <select
                {...register('employmentType')}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {EMPLOYMENT_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value} className="bg-dark-800">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Status
              </label>
              <select
                {...register('status')}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {STATUS_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value} className="bg-dark-800">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Hire Date *
              </label>
              <input
                {...register('hireDate', { required: 'Hire date is required' })}
                type="date"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              {errors.hireDate && (
                <p className="text-red-400 text-sm mt-1">{errors.hireDate.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Job Title
              </label>
              <input
                {...register('jobTitle')}
                type="text"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter job title (alternative to position)"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Personal Information */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiUser className="w-4 h-4" />
            <span>Personal Information</span>
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Gender
              </label>
              <select
                {...register('gender')}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="" className="bg-dark-800">Select Gender</option>
                {GENDER_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value} className="bg-dark-800">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Birth Date
              </label>
              <input
                {...register('birthDate')}
                type="date"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Marital Status
              </label>
              <select
                {...register('maritalStatus')}
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="" className="bg-dark-800">Select Marital Status</option>
                {MARITAL_STATUS_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value} className="bg-dark-800">
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                National ID
              </label>
              <input
                {...register('nationalId')}
                type="text"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter national ID"
              />
            </div>
          </div>
        </div>

        {/* Employment Details */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiBriefcase className="w-4 h-4" />
            <span>Employment Details</span>
          </h4>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Start Date
              </label>
              <input
                {...register('startDate')}
                type="date"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Salary (USD)
              </label>
              <input
                {...register('salary', {
                  valueAsNumber: true,
                  min: { value: 0, message: 'Salary must be positive' }
                })}
                type="number"
                min="0"
                step="100"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter annual salary"
              />
              {errors.salary && (
                <p className="text-red-400 text-sm mt-1">{errors.salary.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Emergency Contact
              </label>
              <input
                {...register('emergencyContact')}
                type="text"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter emergency contact name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-primary-300 mb-2">
                Emergency Phone
              </label>
              <input
                {...register('emergencyPhone')}
                type="tel"
                className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter emergency contact phone"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bio Section */}
      <div className="space-y-4">
        <h4 className="text-white font-medium">Bio & Additional Information</h4>
        <div>
          <label className="block text-sm font-medium text-primary-300 mb-2">
            Bio
          </label>
          <textarea
            {...register('bio')}
            rows={4}
            className="w-full px-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-vertical"
            placeholder="Enter employee bio or description..."
          />
        </div>
      </div>
    </div>
  </GlassCard>
);

// Technologies Step
interface TechnologiesStepProps {
  technologySearchQuery: string;
  setTechnologySearchQuery: (query: string) => void;
  filteredTechnologies: Technology[];
  selectedTechnologies: TechnologySelection[];
  onTechnologySelect: (technology: Technology) => void;
  onTechnologyRemove: (technologyId: string) => void;
  onProficiencyChange: (technologyId: string, level: number) => void;
  technologiesLoading: boolean;
}

export const TechnologiesStep: React.FC<TechnologiesStepProps> = ({
  technologySearchQuery,
  setTechnologySearchQuery,
  filteredTechnologies,
  selectedTechnologies,
  onTechnologySelect,
  onTechnologyRemove,
  onProficiencyChange,
  technologiesLoading
}) => (
  <GlassCard className="p-6">
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-white mb-2">Technologies & Skills</h3>
        <p className="text-primary-300">
          Select the technologies this employee works with and set their proficiency levels.
        </p>
      </div>

      {/* Search Input */}
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Search technologies..."
          value={technologySearchQuery}
          onChange={(e) => setTechnologySearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-dark-800/50 border border-dark-700 rounded-lg text-white placeholder-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* Available Technologies */}
      {technologySearchQuery && (
        <div className="space-y-2">
          <h4 className="text-white font-medium">Available Technologies</h4>
          {technologiesLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-primary-500/30 border-t-primary-500 rounded-full animate-spin" />
            </div>
          ) : filteredTechnologies.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
              {filteredTechnologies.map((technology) => (
                <motion.div
                  key={technology._id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="p-3 bg-dark-800/30 border border-dark-700 rounded-lg hover:bg-dark-800/50 hover:border-primary-500/50 cursor-pointer transition-all duration-200"
                  onClick={() => onTechnologySelect(technology)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-lg flex items-center justify-center overflow-hidden">
                      {technology.icon ? (
                        <img
                          src={technology.icon}
                          alt={technology.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-full h-full bg-gradient-to-br from-primary-500 to-accent-purple rounded-lg flex items-center justify-center ${technology.icon ? 'hidden' : ''}`}>
                        <FiCode className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-white font-medium truncate">{technology.name}</div>
                      <div className="text-primary-300 text-sm truncate">{technology.category}</div>
                    </div>
                    <FiPlus className="w-4 h-4 text-primary-400" />
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-primary-400">
              No technologies found matching your search.
            </div>
          )}
        </div>
      )}

      {/* Selected Technologies */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-white font-medium">Selected Technologies</h4>
          <span className="text-primary-400 text-sm">
            {selectedTechnologies.length} selected
          </span>
        </div>

        {selectedTechnologies.length > 0 ? (
          <div className="space-y-3">
            {selectedTechnologies.map((selection) => (
              <motion.div
                key={selection.technologyId}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="p-4 bg-dark-800/50 border border-dark-700 rounded-lg"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center overflow-hidden">
                      {selection.technology.icon ? (
                        <img
                          src={selection.technology.icon}
                          alt={selection.technology.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-full h-full bg-gradient-to-br from-primary-500 to-accent-purple rounded-lg flex items-center justify-center ${selection.technology.icon ? 'hidden' : ''}`}>
                        <FiCode className="w-5 h-5 text-white" />
                      </div>
                    </div>
                    <div>
                      <div className="text-white font-medium">{selection.technology.name}</div>
                      <div className="text-primary-300 text-sm">{selection.technology.category}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-primary-400 font-medium">
                      {selection.proficiencyLevel}%
                    </span>
                    <button
                      type="button"
                      onClick={() => onTechnologyRemove(selection.technologyId)}
                      className="p-1 text-red-400 hover:text-red-300 transition-colors"
                    >
                      <FiX className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Proficiency Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-primary-300">Proficiency Level</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-primary-400">Beginner</span>
                      <span className="text-primary-300">•</span>
                      <span className="text-primary-400">Expert</span>
                    </div>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={selection.proficiencyLevel}
                      onChange={(e) => onProficiencyChange(selection.technologyId, parseInt(e.target.value))}
                      className="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div
                      className="absolute top-0 left-0 h-2 bg-gradient-to-r from-primary-500 to-accent-purple rounded-lg pointer-events-none"
                      style={{ width: `${selection.proficiencyLevel}%` }}
                    />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-primary-400">
            <FiCode className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>No technologies selected yet.</p>
            <p className="text-sm mt-1">Search and select technologies above to add them.</p>
          </div>
        )}
      </div>
    </div>
  </GlassCard>
);

// Review Step
interface ReviewStepProps {
  formData: CreateEmployeeData;
  selectedUser: UserSearchResult | null;
  createNewUser: boolean;
  selectedTechnologies: TechnologySelection[];
}

export const ReviewStep: React.FC<ReviewStepProps> = ({
  formData,
  selectedUser,
  createNewUser,
  selectedTechnologies
}) => (
  <GlassCard className="p-6">
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-white mb-2">Review & Confirm</h3>
        <p className="text-primary-300">
          Please review all the information before creating the employee record.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Employee Information */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiUser className="w-4 h-4" />
            <span>Employee Information</span>
          </h4>
          <div className="space-y-3 p-4 bg-dark-800/30 rounded-lg">
            <div className="flex justify-between">
              <span className="text-primary-300">Name:</span>
              <span className="text-white">{formData.firstName} {formData.lastName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-primary-300">Email:</span>
              <span className="text-white">{formData.email}</span>
            </div>
            {formData.phone && (
              <div className="flex justify-between">
                <span className="text-primary-300">Phone:</span>
                <span className="text-white">{formData.phone}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-primary-300">Position:</span>
              <span className="text-white">{formData.position}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-primary-300">Department:</span>
              <span className="text-white">
                {DEPARTMENT_OPTIONS.find(d => d.value === formData.department)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-primary-300">Employment Type:</span>
              <span className="text-white">
                {EMPLOYMENT_TYPE_OPTIONS.find(e => e.value === formData.employmentType)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-primary-300">Status:</span>
              <span className="text-white">
                {STATUS_OPTIONS.find(s => s.value === formData.status)?.label}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-primary-300">Hire Date:</span>
              <span className="text-white">
                {formData.hireDate ? new Date(formData.hireDate).toLocaleDateString() : 'Not set'}
              </span>
            </div>
          </div>
        </div>

        {/* User Account Information */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiUsers className="w-4 h-4" />
            <span>User Account</span>
          </h4>
          <div className="p-4 bg-dark-800/30 rounded-lg">
            {selectedUser ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-green-400">
                  <FiCheck className="w-4 h-4" />
                  <span className="font-medium">Linking to existing user</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-primary-300">User:</span>
                    <span className="text-white">{selectedUser.firstName} {selectedUser.lastName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-300">Email:</span>
                    <span className="text-white">{selectedUser.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-300">Role:</span>
                    <span className="text-white capitalize">{selectedUser.role}</span>
                  </div>
                </div>
              </div>
            ) : createNewUser ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-blue-400">
                  <FiPlus className="w-4 h-4" />
                  <span className="font-medium">Creating new user account</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-primary-300">Name:</span>
                    <span className="text-white">{formData.firstName} {formData.lastName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-300">Email:</span>
                    <span className="text-white">{formData.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-primary-300">Role:</span>
                    <span className="text-white">Employee</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-yellow-400">
                <FiAlertCircle className="w-4 h-4" />
                <span>No user account will be created</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Technologies */}
      {selectedTechnologies.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <FiCode className="w-4 h-4" />
            <span>Technologies & Skills ({selectedTechnologies.length})</span>
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {selectedTechnologies.map((selection) => (
              <div key={selection.technologyId} className="p-3 bg-dark-800/30 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">{selection.technology.name}</span>
                  <span className="text-primary-400 text-sm font-medium">
                    {selection.proficiencyLevel}%
                  </span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-primary-500 to-accent-purple h-2 rounded-full transition-all duration-300"
                    style={{ width: `${selection.proficiencyLevel}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  </GlassCard>
);
