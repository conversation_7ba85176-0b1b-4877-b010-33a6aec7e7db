'use client';

import React from 'react';
import Image from 'next/image';
import {
  FiEye,
  FiEdit,
  FiTrash2,
  FiStar,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiBriefcase,
  FiCalendar
} from 'react-icons/fi';
import { Employee, EmployeeDepartment, EmployeeStatus } from '@/lib/types/employee';
import { GlassCard } from '@/components/ui/glass-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';

interface EmployeeGridProps {
  employees: Employee[];
  isLoading?: boolean;
  selectedIds: string[];
  onSelect: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  onView: (employee: Employee) => void;
  onEdit: (employee: Employee) => void;
  onDelete: (employee: Employee) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  limit: number;
  onLimitChange: (limit: number) => void;
}

interface EmployeeCardProps {
  employee: Employee;
  isSelected: boolean;
  onSelect: (selected: boolean) => void;
  onView: () => void;
  onEdit: () => void;
  onDelete: (employee: Employee) => void;
}

// Helper function to validate and format image URLs
const getValidImageUrl = (imageUrl: string | undefined): string | null => {
  if (!imageUrl || imageUrl.trim() === '') {
    return null;
  }

  // If it's already a full URL, return it
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    try {
      new URL(imageUrl);
      return imageUrl;
    } catch {
      return null;
    }
  }

  // If it's a relative path, construct the full URL
  if (imageUrl.startsWith('/') || imageUrl.startsWith('media/')) {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';
    const serverBaseUrl = baseUrl.replace('/api/v1', '');
    return `${serverBaseUrl}/${imageUrl.startsWith('/') ? imageUrl.slice(1) : imageUrl}`;
  }

  // For other cases, try to construct a valid URL
  try {
    new URL(imageUrl);
    return imageUrl;
  } catch {
    // If it's not a valid URL, treat it as a relative path
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api/v1';
    const serverBaseUrl = baseUrl.replace('/api/v1', '');
    return `${serverBaseUrl}/${imageUrl}`;
  }
};

// Helper function to get department color
const getDepartmentColor = (department: EmployeeDepartment): string => {
  const colors = {
    [EmployeeDepartment.ENGINEERING]: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    [EmployeeDepartment.DESIGN]: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
    [EmployeeDepartment.MARKETING]: 'bg-pink-500/20 text-pink-400 border-pink-500/30',
    [EmployeeDepartment.HR]: 'bg-green-500/20 text-green-400 border-green-500/30',
    [EmployeeDepartment.FINANCE]: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    [EmployeeDepartment.OPERATIONS]: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
    [EmployeeDepartment.SALES]: 'bg-red-500/20 text-red-400 border-red-500/30',
    [EmployeeDepartment.PRODUCT]: 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30',
    [EmployeeDepartment.QUALITY_ASSURANCE]: 'bg-teal-500/20 text-teal-400 border-teal-500/30',
    [EmployeeDepartment.CUSTOMER_SUPPORT]: 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30',
    [EmployeeDepartment.LEGAL]: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
    [EmployeeDepartment.OTHER]: 'bg-slate-500/20 text-slate-400 border-slate-500/30'
  };
  return colors[department] || colors[EmployeeDepartment.OTHER];
};

// Helper function to get status color
const getStatusColor = (status: EmployeeStatus): string => {
  const colors = {
    [EmployeeStatus.ACTIVE]: 'bg-green-500/20 text-green-400 border-green-500/30',
    [EmployeeStatus.INACTIVE]: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
    [EmployeeStatus.TERMINATED]: 'bg-red-500/20 text-red-400 border-red-500/30',
    [EmployeeStatus.ON_LEAVE]: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
    [EmployeeStatus.PROBATION]: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
    [EmployeeStatus.NOTICE_PERIOD]: 'bg-purple-500/20 text-purple-400 border-purple-500/30'
  };
  return colors[status] || colors[EmployeeStatus.INACTIVE];
};

// Helper function to format department name
const formatDepartmentName = (department: EmployeeDepartment): string => {
  const names = {
    [EmployeeDepartment.ENGINEERING]: 'Engineering',
    [EmployeeDepartment.DESIGN]: 'Design',
    [EmployeeDepartment.MARKETING]: 'Marketing',
    [EmployeeDepartment.HR]: 'HR',
    [EmployeeDepartment.FINANCE]: 'Finance',
    [EmployeeDepartment.OPERATIONS]: 'Operations',
    [EmployeeDepartment.SALES]: 'Sales',
    [EmployeeDepartment.PRODUCT]: 'Product',
    [EmployeeDepartment.QUALITY_ASSURANCE]: 'QA',
    [EmployeeDepartment.CUSTOMER_SUPPORT]: 'Support',
    [EmployeeDepartment.LEGAL]: 'Legal',
    [EmployeeDepartment.OTHER]: 'Other'
  };
  return names[department] || 'Other';
};

// Helper function to format status name
const formatStatusName = (status: EmployeeStatus): string => {
  const names = {
    [EmployeeStatus.ACTIVE]: 'Active',
    [EmployeeStatus.INACTIVE]: 'Inactive',
    [EmployeeStatus.TERMINATED]: 'Terminated',
    [EmployeeStatus.ON_LEAVE]: 'On Leave',
    [EmployeeStatus.PROBATION]: 'Probation',
    [EmployeeStatus.NOTICE_PERIOD]: 'Notice Period'
  };
  return names[status] || 'Unknown';
};

const EmployeeCard: React.FC<EmployeeCardProps> = ({
  employee,
  isSelected,
  onSelect,
  onView,
  onEdit,
  onDelete
}) => {
  const imageUrl = getValidImageUrl(employee.profileImage) || getValidImageUrl(employee.avatar);
  const fullName = `${employee.firstName} ${employee.lastName}`;

  // Calculate tenure
  const hireDate = new Date(employee.hireDate);
  const now = new Date();
  const tenureYears = ((now.getTime() - hireDate.getTime()) / (1000 * 60 * 60 * 24 * 365)).toFixed(1);

  return (
    <div className="group">
      <GlassCard className={`relative overflow-hidden hover:shadow-2xl hover:shadow-primary-500/10 transition-all duration-500 ${
        isSelected ? 'ring-2 ring-primary-500 bg-primary-500/5' : ''
      }`}>
        {/* Background Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 via-transparent to-accent-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Header with checkbox and actions */}
        <div className="relative p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => onSelect(e.target.checked)}
                className="w-4 h-4 text-primary-500 bg-dark-700 border-dark-600 rounded focus:ring-primary-500 focus:ring-2"
              />
              {employee.isFeatured && (
                <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-full">
                  <FiStar className="w-3 h-3 text-yellow-400 fill-current" />
                  <span className="text-xs font-medium text-yellow-400">Featured</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
              <EnhancedButton
                variant="secondary"
                size="sm"
                onClick={onView}
                className="p-2 bg-dark-800/50 border-dark-600 hover:bg-primary-500/20 hover:border-primary-500/50"
                title="View Details"
              >
                <FiEye className="w-4 h-4" />
              </EnhancedButton>
              <EnhancedButton
                variant="secondary"
                size="sm"
                onClick={onEdit}
                className="p-2 bg-dark-800/50 border-dark-600 hover:bg-blue-500/20 hover:border-blue-500/50"
                title="Edit Employee"
              >
                <FiEdit className="w-4 h-4" />
              </EnhancedButton>
              <EnhancedButton
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onDelete(employee);
                }}
                className="p-2 bg-dark-800/50 border-dark-600 hover:bg-red-500/20 hover:border-red-500/50 text-red-400"
                title={`Delete ${employee.firstName} ${employee.lastName}`}
              >
                <FiTrash2 className="w-4 h-4" />
              </EnhancedButton>
            </div>
          </div>

          {/* Profile Section */}
          <div className="flex flex-col items-center text-center mb-6">
            <div className="relative mb-4">
              {imageUrl ? (
                <div className="w-20 h-20 relative">
                  <Image
                    src={imageUrl}
                    alt={fullName}
                    fill
                    className="object-cover rounded-full ring-4 ring-dark-700/50 group-hover:ring-primary-500/30 transition-all duration-300"
                    onError={() => {
                      console.error('Image failed to load:', imageUrl);
                    }}
                  />
                </div>
              ) : (
                <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-accent-purple rounded-full flex items-center justify-center ring-4 ring-dark-700/50 group-hover:ring-primary-500/30 transition-all duration-300">
                  <FiUser className="w-10 h-10 text-white" />
                </div>
              )}
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-3 border-dark-800 flex items-center justify-center ${
                employee.status === EmployeeStatus.ACTIVE ? 'bg-green-500' : 'bg-gray-500'
              }`}>
                <div className="w-2 h-2 bg-white rounded-full" />
              </div>
            </div>

            <div className="space-y-1">
              <h3 className="text-xl font-bold text-white group-hover:text-primary-300 transition-colors duration-300">{fullName}</h3>
              <p className="text-primary-300 font-medium">{employee.position}</p>
              <p className="text-primary-400 text-sm font-mono">#{employee.employeeId}</p>
            </div>
          </div>

          {/* Status and Department Badges */}
          <div className="flex items-center justify-center space-x-2 mb-6">
            <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(employee.status)}`}>
              {formatStatusName(employee.status)}
            </span>
            <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getDepartmentColor(employee.department)}`}>
              {formatDepartmentName(employee.department)}
            </span>
          </div>

          {/* Quick Info Grid */}
          <div className="grid grid-cols-1 gap-3 mb-6">
            <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg border border-dark-700/50">
              <div className="flex items-center space-x-2">
                <FiMail className="w-4 h-4 text-primary-400" />
                <span className="text-sm text-primary-300">Email</span>
              </div>
              <span className="text-sm text-white font-medium truncate max-w-32" title={employee.email}>
                {employee.email}
              </span>
            </div>

            {employee.phone && (
              <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg border border-dark-700/50">
                <div className="flex items-center space-x-2">
                  <FiPhone className="w-4 h-4 text-primary-400" />
                  <span className="text-sm text-primary-300">Phone</span>
                </div>
                <span className="text-sm text-white font-medium">{employee.phone}</span>
              </div>
            )}

            <div className="flex items-center justify-between p-3 bg-dark-800/30 rounded-lg border border-dark-700/50">
              <div className="flex items-center space-x-2">
                <FiCalendar className="w-4 h-4 text-primary-400" />
                <span className="text-sm text-primary-300">Tenure</span>
              </div>
              <span className="text-sm text-white font-medium">{tenureYears} years</span>
            </div>
          </div>

          {/* Performance & Stats Grid */}
          <div className="grid grid-cols-2 gap-3 mb-6">
            {employee.performanceRating && (
              <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded-lg p-3 text-center">
                <div className="text-green-400 text-xs font-medium mb-1">Performance</div>
                <div className="text-white font-bold text-lg">{employee.performanceRating}/5</div>
                <div className="w-full bg-dark-700 rounded-full h-1.5 mt-2">
                  <div
                    className="bg-green-500 h-1.5 rounded-full transition-all duration-500"
                    style={{ width: `${(employee.performanceRating / 5) * 100}%` }}
                  />
                </div>
              </div>
            )}

            {(employee.projects?.length || 0) > 0 && (
              <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded-lg p-3 text-center">
                <div className="text-blue-400 text-xs font-medium mb-1">Projects</div>
                <div className="text-white font-bold text-lg">{employee.projects?.length || 0}</div>
                <div className="text-blue-300 text-xs mt-1">Active</div>
              </div>
            )}

            {employee.salary && (
              <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 border border-yellow-500/20 rounded-lg p-3 text-center col-span-2">
                <div className="text-yellow-400 text-xs font-medium mb-1">Annual Salary</div>
                <div className="text-white font-bold text-lg">${employee.salary.toLocaleString()}</div>
              </div>
            )}

            {(employee.trainings?.length || 0) > 0 && !employee.salary && (
              <div className="bg-gradient-to-br from-purple-500/10 to-purple-600/5 border border-purple-500/20 rounded-lg p-3 text-center">
                <div className="text-purple-400 text-xs font-medium mb-1">Trainings</div>
                <div className="text-white font-bold text-lg">{employee.trainings?.length || 0}</div>
                <div className="text-purple-300 text-xs mt-1">Completed</div>
              </div>
            )}
          </div>

          {/* Hire Date */}
          <div className="flex items-center justify-center space-x-2 text-sm text-primary-400 bg-dark-800/20 rounded-lg p-3 border border-dark-700/50">
            <FiCalendar className="w-4 h-4" />
            <span>Joined {new Date(employee.hireDate).toLocaleDateString()}</span>
          </div>

          {/* Technologies & Skills */}
          {((employee.technologySkills && employee.technologySkills.length > 0) || (employee.skills && employee.skills.length > 0)) && (
            <div className="mt-4 pt-4 border-t border-dark-600">
              {employee.technologySkills && employee.technologySkills.length > 0 && (
                <div className="mb-3">
                  <p className="text-xs text-primary-400 mb-2">Technologies ({employee.technologySkills.length})</p>
                  <div className="flex flex-wrap gap-1">
                    {employee.technologySkills.slice(0, 3).map((tech, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-dark-700/50 text-primary-300 text-xs rounded-md"
                      >
                        {tech.technology?.name || 'Unknown'}
                      </span>
                    ))}
                    {employee.technologySkills.length > 3 && (
                      <span className="px-2 py-1 bg-dark-700/50 text-primary-400 text-xs rounded-md">
                        +{employee.technologySkills.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {employee.skills && employee.skills.length > 0 && (
                <div>
                  <p className="text-xs text-primary-400 mb-2">Skills ({employee.skills.length})</p>
                  <div className="flex flex-wrap gap-1">
                    {employee.skills.slice(0, 4).map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary-500/20 text-primary-300 text-xs rounded-md"
                      >
                        {skill}
                      </span>
                    ))}
                    {employee.skills.length > 4 && (
                      <span className="px-2 py-1 bg-primary-500/20 text-primary-400 text-xs rounded-md">
                        +{employee.skills.length - 4} more
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </GlassCard>
    </div>
  );
};

export const EmployeeGrid: React.FC<EmployeeGridProps> = ({
  employees,
  isLoading = false,
  selectedIds,
  onSelect,
  onSelectAll,
  onView,
  onEdit,
  onDelete,
  currentPage,
  totalPages,
  onPageChange,
  limit,
  onLimitChange
}) => {
  const allSelected = employees.length > 0 && employees.every(emp => selectedIds.includes(emp._id));
  const someSelected = selectedIds.length > 0 && !allSelected;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: limit }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <GlassCard className="p-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-dark-700/50 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-dark-700/50 rounded w-3/4" />
                    <div className="h-3 bg-dark-700/50 rounded w-1/2" />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-dark-700/50 rounded" />
                  <div className="h-3 bg-dark-700/50 rounded w-2/3" />
                </div>
              </div>
            </GlassCard>
          </div>
        ))}
      </div>
    );
  }

  if (employees.length === 0) {
    return (
      <GlassCard className="p-12 text-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-dark-700/50 rounded-full flex items-center justify-center">
            <FiUser className="w-8 h-8 text-primary-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-2">No employees found</h3>
            <p className="text-primary-300">Try adjusting your search criteria or add a new employee.</p>
          </div>
        </div>
      </GlassCard>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header with select all */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <label className="flex cursor-pointer items-center space-x-2">
            <input
              type="checkbox"
              checked={allSelected}
              ref={(input) => {
                if (input) input.indeterminate = someSelected;
              }}
              onChange={(e) => onSelectAll(e.target.checked)}
              className="size-4 rounded border-dark-600 bg-dark-700 text-primary-500 focus:ring-2 focus:ring-primary-500"
            />
            <span className="text-sm text-primary-300">
              {allSelected ? 'Deselect all' : someSelected ? 'Select all' : 'Select all'}
            </span>
          </label>
          {selectedIds.length > 0 && (
            <span className="text-sm text-primary-400">
              {selectedIds.length} employee{selectedIds.length !== 1 ? 's' : ''} selected
            </span>
          )}
        </div>

        {/* Items per page */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-primary-300">Show:</span>
          <select
            value={limit}
            onChange={(e) => onLimitChange(Number(e.target.value))}
            className="bg-dark-700 border border-dark-600 rounded-md px-3 py-1 text-sm text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          <span className="text-sm text-primary-300">per page</span>
        </div>
      </div>

      {/* Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  gap-6">
        {employees.map((employee) => (
          <EmployeeCard
            key={employee._id}
            employee={employee}
            isSelected={selectedIds.includes(employee._id)}
            onSelect={(selected) => onSelect(employee._id, selected)}
            onView={() => onView(employee)}
            onEdit={() => onEdit(employee)}
            onDelete={onDelete}
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <EnhancedButton
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Previous
          </EnhancedButton>

          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + 1;
            return (
              <EnhancedButton
                key={page}
                variant={currentPage === page ? "primary" : "secondary"}
                size="sm"
                onClick={() => onPageChange(page)}
              >
                {page}
              </EnhancedButton>
            );
          })}

          {totalPages > 5 && currentPage < totalPages - 2 && (
            <>
              <span className="text-primary-400">...</span>
              <EnhancedButton
                variant="secondary"
                size="sm"
                onClick={() => onPageChange(totalPages)}
              >
                {totalPages}
              </EnhancedButton>
            </>
          )}

          <EnhancedButton
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Next
          </EnhancedButton>
        </div>
      )}
    </div>
  );
};
