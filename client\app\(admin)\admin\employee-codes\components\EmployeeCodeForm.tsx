import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { adminEmployeeCodesApi } from '@/lib/api/admin/employeeCodes';
import { EmployeeCodeRole } from '@/lib/types/employee-code';
import { FiKey, FiFileText, FiUser } from 'react-icons/fi';

const schema = z.object({
  code: z.string().min(4, 'Code must be at least 4 characters').max(10, 'Code must be at most 10 characters'),
  description: z.string().min(1, 'Description is required'),
  role: z.nativeEnum(EmployeeCodeRole),
});

type FormValues = z.infer<typeof schema>;

export default function EmployeeCodeForm() {
  const { register, handleSubmit, reset, formState: { errors, isSubmitting } } = useForm<FormValues>({
    resolver: zodR<PERSON><PERSON>ver(schema),
  });
  const [loading, setLoading] = useState(false);

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    try {
      await adminEmployeeCodesApi.create(data);
      toast.success('Employee code created successfully');
      reset();
    } catch (e: any) {
      toast.error(e.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };



  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Employee Code Field */}
      <div className="space-y-3">
        <label className="flex items-center gap-2 text-sm font-semibold text-white">
          <FiKey className="w-4 h-4 text-blue-400" />
          Employee Code
        </label>
        <div className="relative group">
          <input
            {...register('code')}
            minLength={4}
            maxLength={10}
            className={`w-full px-4 py-3 pl-12 bg-slate-800/50 backdrop-blur-sm border-2 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500 transition-all duration-300 ${
              errors.code ? 'border-red-500/50 focus:border-red-500' : 'border-slate-600/50 hover:border-blue-500/30'
            }`}
            placeholder="Enter unique code (4-10 characters)"
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-4">
            <FiKey className="w-4 h-4 text-slate-400 group-focus-within:text-blue-400 transition-colors duration-300" />
          </div>
        </div>
        {errors.code && (
          <div className="flex items-center gap-2 mt-2 text-sm text-red-400 animate-fade-in">
            <div className="w-1 h-1 bg-red-400 rounded-full"></div>
            <span>{errors.code.message}</span>
          </div>
        )}
      </div>

      {/* Description Field */}
      <div className="space-y-3">
        <label className="flex items-center gap-2 text-sm font-semibold text-white">
          <FiFileText className="w-4 h-4 text-green-400" />
          Description
        </label>
        <div className="relative group">
          <textarea
            {...register('description')}
            rows={4}
            className={`w-full px-4 py-3 bg-slate-800/50 backdrop-blur-sm border-2 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500 transition-all duration-300 resize-none ${
              errors.description ? 'border-red-500/50 focus:border-red-500' : 'border-slate-600/50 hover:border-green-500/30'
            }`}
            placeholder="Describe the purpose and usage of this employee code..."
          />
        </div>
        {errors.description && (
          <div className="flex items-center gap-2 mt-2 text-sm text-red-400 animate-fade-in">
            <div className="w-1 h-1 bg-red-400 rounded-full"></div>
            <span>{errors.description.message}</span>
          </div>
        )}
      </div>

      {/* Role Field */}
      <div className="space-y-3">
        <label className="flex items-center gap-2 text-sm font-semibold text-white">
          <FiUser className="w-4 h-4 text-purple-400" />
          Role Assignment
        </label>
        <div className="relative group">
          <select
            {...register('role')}
            className={`w-full px-4 py-3 bg-slate-800/50 backdrop-blur-sm border-2 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 transition-all duration-300 appearance-none cursor-pointer ${
              errors.role ? 'border-red-500/50 focus:border-red-500' : 'border-slate-600/50 hover:border-purple-500/30'
            }`}
          >
            <option value="" className="bg-slate-800 text-slate-300">Select role level</option>
            <option value={EmployeeCodeRole.EMPLOYEE} className="bg-slate-800 text-white">👤 Employee</option>
            <option value={EmployeeCodeRole.TEAM_LEADER} className="bg-slate-800 text-white">👥 Team Leader</option>
            <option value={EmployeeCodeRole.PROJECT_MANAGER} className="bg-slate-800 text-white">📋 Project Manager</option>
            <option value={EmployeeCodeRole.ADMIN} className="bg-slate-800 text-white">⭐ Administrator</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg className="w-4 h-4 text-slate-400 group-focus-within:text-purple-400 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        {errors.role && (
          <div className="flex items-center gap-2 mt-2 text-sm text-red-400 animate-fade-in">
            <div className="w-1 h-1 bg-red-400 rounded-full"></div>
            <span>{errors.role.message}</span>
          </div>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          disabled={loading || isSubmitting}
          className="w-full py-4 px-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-3"
        >
          {loading || isSubmitting ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Creating Code...</span>
            </>
          ) : (
            <>
              <FiKey className="w-5 h-5" />
              <span>Generate Employee Code</span>
            </>
          )}
        </button>
      </div>
    </form>
  );
}
