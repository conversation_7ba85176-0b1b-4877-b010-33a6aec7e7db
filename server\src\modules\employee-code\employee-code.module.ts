import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EmployeeCode, EmployeeCodeSchema } from './employee-code.schema';
import { EmployeeCodeService } from './employee-code.service';
import { EmployeeCodeController } from './employee-code.controller';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: EmployeeCode.name, schema: EmployeeCodeSchema }]),
  ],
  providers: [EmployeeCodeService],
  controllers: [EmployeeCodeController],
  exports: [EmployeeCodeService],
})
export class EmployeeCodeModule {}
