'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardHeader } from '@/components/shared/Card';
import { FormField, Input, Checkbox } from '@/components/shared/forms/FormField';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { useLogin } from '@/lib/hooks/auth/useLogin';
import { useErrorHandler } from '@/lib/utils/error-handler';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
  isAdmin: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

const LoginPage = () => {
  const { mutate, isPending } = useLogin();
  const [showPassword, setShowPassword] = useState(false);
  const [formError, setFormError] = useState('');
  const router = useRouter();
  const { handleFormError } = useErrorHandler();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
      isAdmin: false,
    },
  });

  const onSubmit = (data: LoginFormData) => {
    setFormError(''); // Clear previous errors

    // Ensure isAdmin is properly typed
    const loginData = {
      email: data.email,
      password: data.password,
      isAdmin: data.isAdmin || false,
      rememberMe: data.rememberMe || false
    };

    mutate(loginData, {
      onError: (error) => {
        // Use the error handler to prevent global errors and show toast
        handleFormError(error, setFormError);
      }
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card className="p-8">
          <CardHeader
            title="Welcome to SillaLink"
            subtitle="Sign in to your account to continue"
            className="text-center mb-6"
          />

          {formError && (
            <Alert
              type="error"
              message={formError}
              closable
              onClose={() => setFormError('')}
              className="mb-6"
            />
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              label="Email Address"
              required
              error={errors.email?.message}
            >
              <Input
                {...register('email')}
                type="email"
                placeholder="Enter your email address"
                error={!!errors.email}
                disabled={isPending}
              />
            </FormField>

            <FormField
              label="Password"
              required
              error={errors.password?.message}
            >
              <div className="relative">
                <Input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  error={!!errors.password}
                  disabled={isPending}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  )}
                </button>
              </div>
            </FormField>

            <div className="flex items-center justify-between">
              <FormField>
                <Checkbox
                  {...register('rememberMe')}
                  label="Remember me"
                  disabled={isPending}
                />
              </FormField>

              <Link
                href="/auth/forgot-password"
                className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Forgot password?
              </Link>
            </div>

            <FormField>
              <Checkbox
                {...register('isAdmin')}
                label="Admin Login"
                disabled={isPending}
              />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Check this if you're logging in as an administrator
              </p>
            </FormField>

            <Button
              type="submit"
              fullWidth
              loading={isPending}
              loadingText="Signing in..."
              size="lg"
            >
              Sign In
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Don't have an account?{' '}
              <Link
                href="/auth/register"
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Register with employee code
              </Link>
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
