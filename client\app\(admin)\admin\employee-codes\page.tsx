"use client";
import EmployeeCodeForm from './components/EmployeeCodeForm';
import EmployeeCodeTable from './components/EmployeeCodeTable';
import CheckCodeBox from './components/CheckCodeBox';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck<PERSON><PERSON>cle, <PERSON><PERSON>ist, <PERSON><PERSON>hield, Fi<PERSON>sers, FiPlus } from 'react-icons/fi';

export default function EmployeeCodesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="text-center space-y-6 animate-fade-in">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 text-white shadow-2xl shadow-blue-500/25">
            <FiShield className="w-8 h-8" />
          </div>
          <div className="space-y-2">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">
              Employee Code Management
            </h1>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Secure access control through employee verification codes
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="group bg-gradient-to-br from-blue-500/10 to-blue-600/10 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6 text-center hover:border-blue-500/40 transition-all duration-300 hover:scale-105">
            <FiUsers className="w-8 h-8 text-blue-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
            <h3 className="text-2xl font-bold text-white mb-1">Active Codes</h3>
            <p className="text-blue-300">Manage employee access</p>
          </div>
          <div className="group bg-gradient-to-br from-green-500/10 to-green-600/10 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6 text-center hover:border-green-500/40 transition-all duration-300 hover:scale-105">
            <FiCheckCircle className="w-8 h-8 text-green-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
            <h3 className="text-2xl font-bold text-white mb-1">Verification</h3>
            <p className="text-green-300">Validate code status</p>
          </div>
          <div className="group bg-gradient-to-br from-purple-500/10 to-purple-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 text-center hover:border-purple-500/40 transition-all duration-300 hover:scale-105">
            <FiPlus className="w-8 h-8 text-purple-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
            <h3 className="text-2xl font-bold text-white mb-1">Create New</h3>
            <p className="text-purple-300">Generate access codes</p>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <div className="bg-gradient-to-br from-blue-500/5 to-purple-500/5 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6 hover:border-blue-500/30 transition-all duration-300">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                <FiKey className="text-white w-5 h-5" />
              </div>
              <h2 className="text-2xl font-bold text-white">Create New Code</h2>
            </div>
            <EmployeeCodeForm />
          </div>

          <div className="bg-gradient-to-br from-green-500/5 to-emerald-500/5 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6 hover:border-green-500/30 transition-all duration-300">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                <FiCheckCircle className="text-white w-5 h-5" />
              </div>
              <h2 className="text-2xl font-bold text-white">Verify Code</h2>
            </div>
            <CheckCodeBox />
          </div>
        </div>

        {/* Employee Codes Table */}
        <div className="bg-gradient-to-br from-purple-500/5 to-indigo-500/5 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 hover:border-purple-500/30 transition-all duration-300">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
              <FiList className="text-white w-5 h-5" />
            </div>
            <h2 className="text-2xl font-bold text-white">Unused Employee Codes</h2>
          </div>
          <EmployeeCodeTable />
        </div>
      </div>
    </div>
  );
}
