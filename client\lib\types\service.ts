// Service Management Types
export enum ServiceCategory {
  WEB_DEVELOPMENT = 'WEB_DEVELOPMENT',
  MO<PERSON>LE_DEVELOPMENT = 'MOBILE_DEVELOPMENT',
  UI_UX_DESIGN = 'UI_UX_DESIGN',
  CONSULTING = 'CONSULTING',
  MAINTENANCE = 'MAINTENANCE',
  TESTING = 'TESTING',
  DEVOPS = 'DEVOPS',
  AI_ML = 'AI_ML',
  BLOCKCHAIN = 'BLOCKCHAIN',
  E_COMMERCE = 'E_COMMERCE',
  OTHER = 'OTHER'
}

export enum ServiceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  COMING_SOON = 'COMING_SOON',
  DEPRECATED = 'DEPRECATED'
}

export enum PricingModel {
  FIXED = 'FIXED',
  HOURLY = 'HOURLY',
  PROJECT_BASED = 'PROJECT_BASED',
  SUBSCRIPTION = 'SUBSCRIPTION',
  CUSTOM = 'CUSTOM'
}

export interface ServiceFeature {
  id: string;
  name: string;
  description: string;
  isIncluded: boolean;
  isPremium?: boolean;
}

export interface ServicePackage {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration?: number; // in days
  features: ServiceFeature[];
  isPopular?: boolean;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  category: ServiceCategory;
  status: ServiceStatus;
  pricingModel: PricingModel;
  basePrice?: number;
  currency: string;
  duration?: number; // estimated duration in days
  features: ServiceFeature[];
  packages: ServicePackage[];
  technologies: string[];
  deliverables: string[];
  requirements: string[];
  mainImage?: string;
  images: string[];
  portfolioProjects: string[];
  teamMembers: string[];
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  orderCount: number;
  averageRating: number;
  totalReviews: number;
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateServiceDto {
  name: string;
  description: string;
  longDescription?: string;
  category: ServiceCategory;
  status?: ServiceStatus;
  pricingModel: PricingModel;
  basePrice?: number;
  currency?: string;
  duration?: number;
  features?: ServiceFeature[];
  packages?: ServicePackage[];
  technologies?: string[];
  deliverables?: string[];
  requirements?: string[];
  portfolioProjects?: string[];
  teamMembers?: string[];
  tags?: string[];
  isFeatured?: boolean;
}

export interface UpdateServiceDto {
  name?: string;
  description?: string;
  longDescription?: string;
  category?: ServiceCategory;
  status?: ServiceStatus;
  pricingModel?: PricingModel;
  basePrice?: number;
  currency?: string;
  duration?: number;
  features?: ServiceFeature[];
  packages?: ServicePackage[];
  technologies?: string[];
  deliverables?: string[];
  requirements?: string[];
  portfolioProjects?: string[];
  teamMembers?: string[];
  tags?: string[];
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface ServiceFilters {
  search?: string;
  category?: ServiceCategory;
  status?: ServiceStatus;
  pricingModel?: PricingModel;
  minPrice?: number;
  maxPrice?: number;
  technology?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
}

export interface ServiceStats {
  total: number;
  active: number;
  featured: number;
  totalOrders: number;
  averageRating: number;
  byCategory: { [key: string]: number };
  byPricingModel: { [key: string]: number };
}

// Service inquiry/order types
export interface ServiceInquiry {
  id: string;
  serviceId: string;
  packageId?: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  company?: string;
  message: string;
  budget?: number;
  timeline?: string;
  requirements?: string[];
  status: 'PENDING' | 'REVIEWED' | 'QUOTED' | 'ACCEPTED' | 'REJECTED';
  assignedTo?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateInquiryDto {
  serviceId: string;
  packageId?: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  company?: string;
  message: string;
  budget?: number;
  timeline?: string;
  requirements?: string[];
}
