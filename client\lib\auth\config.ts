import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { authApi } from '@/lib/api/auth';
import './types'; // Import type extensions

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        isAdmin: { label: 'Admin Login', type: 'checkbox' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Debug logging (remove in production)
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 NextAuth authorize called with:', {
            email: credentials.email,
            isAdmin: credentials.isAdmin,
            isAdminType: typeof credentials.isAdmin
          });
        }

        try {
          // Handle both string and boolean values for isAdmin (NextAuth can pass either)
          const isAdminValue = credentials.isAdmin as string | boolean | undefined;
          const isAdminLogin = isAdminValue === 'true' || isAdminValue === 'on' || isAdminValue === true;

          const response = isAdminLogin
            ? await authApi.adminLogin({
                email: credentials.email,
                password: credentials.password,
              })
            : await authApi.login({
                email: credentials.email,
                password: credentials.password,
              });

          // Handle both old and new response formats
          const user = response.data?.user || (response as any).user;
          const accessToken = response.data?.accessToken || (response as any).accessToken;
          const refreshToken = response.data?.refreshToken || (response as any).refreshToken;

          console.log('🔍 NextAuth Response Debug:', {
            hasSuccess: !!response.success,
            hasDataUser: !!response.data?.user,
            hasDirectUser: !!(response as any).user,
            hasAccessToken: !!accessToken,
            userRole: user?.role
          });

          if (response.success && user) {
            // Store tokens in localStorage for API client
            if (typeof window !== 'undefined') {
              localStorage.setItem('auth-token', accessToken);
              localStorage.setItem('refresh-token', refreshToken);
            }

            return {
              id: user._id,
              email: user.email,
              name: `${user.firstName} ${user.lastName}`,
              role: user.role,
              accessToken: accessToken,
              refreshToken: refreshToken,
            };
          }
          return null;
        } catch (error: any) {
          console.error('❌ NextAuth Authentication error:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data,
            isAxiosError: error.isAxiosError
          });

          // Log the specific error message for debugging
          if (error.response?.data?.message) {
            console.error('Backend error message:', error.response.data.message);
          }

          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role || '';
        token.accessToken = user.accessToken || '';
        token.refreshToken = user.refreshToken || '';
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.role = token.role as string;
      }
      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;
      return session;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/login',
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET || 'fallback-secret-for-development',
};
