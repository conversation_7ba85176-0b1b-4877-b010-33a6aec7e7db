import { useState } from 'react';
import { adminEmployeeCodesApi } from '@/lib/api/admin/employeeCodes';
import { toast } from 'react-hot-toast';
import { FiSearch, FiCheckCircle, FiXCircle, FiKey, FiShield } from 'react-icons/fi';

export default function CheckCodeBox() {
  const [code, setCode] = useState('');
  const [result, setResult] = useState<{ isValid: boolean; message: string } | null>(null);
  const [loading, setLoading] = useState(false);

  const checkCode = async () => {
    if (!code.trim()) {
      toast.error('Please enter a code to verify');
      return;
    }

    setLoading(true);
    setResult(null);
    try {
      await adminEmployeeCodesApi.check(code);
      setResult({
        isValid: true,
        message: 'Code is valid and available for use'
      });
      toast.success('Code verified successfully');
    } catch (e: any) {
      setResult({
        isValid: false,
        message: 'Code is invalid or already used'
      });
      toast.error(e.message || 'Verification failed');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      checkCode();
    }
  };

  return (
    <div className="space-y-6">
      {/* Input Section */}
      <div className="space-y-4">
        <label className="flex items-center gap-2 text-sm font-semibold text-white">
          <FiShield className="w-4 h-4 text-green-400" />
          Code Verification
        </label>

        <div className="relative group">
          <input
            type="text"
            value={code}
            onChange={e => setCode(e.target.value.toUpperCase())}
            onKeyDown={handleKeyDown}
            minLength={4}
            maxLength={10}
            className="w-full px-4 py-3 pl-12 bg-slate-800/50 backdrop-blur-sm border-2 border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500 transition-all duration-300 font-mono text-lg tracking-wider hover:border-green-500/30"
            placeholder="Enter code to verify..."
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-4">
            <FiKey className="w-5 h-5 text-slate-400 group-focus-within:text-green-400 transition-colors duration-300" />
          </div>
        </div>

        <button
          onClick={checkCode}
          disabled={loading || !code.trim()}
          className="w-full py-3 px-6 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center gap-3"
        >
          {loading ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Verifying...</span>
            </>
          ) : (
            <>
              <FiSearch className="w-5 h-5" />
              <span>Verify Code</span>
            </>
          )}
        </button>
      </div>

      {/* Result Section */}
      {result && (
        <div className={`p-4 rounded-xl border-2 animate-fade-in ${
          result.isValid
            ? 'bg-green-500/10 border-green-500/30 backdrop-blur-sm'
            : 'bg-red-500/10 border-red-500/30 backdrop-blur-sm'
        }`}>
          <div className="flex items-center gap-3">
            {result.isValid ? (
              <div className="p-2 bg-green-500/20 rounded-lg">
                <FiCheckCircle className="w-6 h-6 text-green-400" />
              </div>
            ) : (
              <div className="p-2 bg-red-500/20 rounded-lg">
                <FiXCircle className="w-6 h-6 text-red-400" />
              </div>
            )}
            <div>
              <h3 className={`font-semibold ${result.isValid ? 'text-green-400' : 'text-red-400'}`}>
                {result.isValid ? 'Valid Code' : 'Invalid Code'}
              </h3>
              <p className="text-sm text-slate-300 mt-1">
                {result.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Info Section */}
      <div className="p-4 bg-blue-500/10 border border-blue-500/30 rounded-xl backdrop-blur-sm">
        <div className="flex items-start gap-3">
          <div className="p-1 bg-blue-500/20 rounded-lg mt-0.5">
            <FiShield className="w-4 h-4 text-blue-400" />
          </div>
          <div>
            <h4 className="text-sm font-semibold text-blue-400 mb-1">Verification Info</h4>
            <p className="text-xs text-slate-400 leading-relaxed">
              Enter an employee code to check if it's valid and available for registration.
              Valid codes are 4-10 characters long and haven't been used yet.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
