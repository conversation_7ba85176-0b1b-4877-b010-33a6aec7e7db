const axios = require('axios');
const { MongoClient } = require('mongodb');

const API_BASE = 'http://localhost:5000/api/v1';

async function testRegistrationFlow() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('silla_link');
    
    console.log('🧪 Testing Employee Code Registration Flow with Automatic Employee Creation\n');
    
    // Test user data
    const testUser = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      functionalCode: 'TEST001',
      password: 'TestPassword123!'
    };
    
    console.log('📋 Test User Details:');
    console.log(`  - Name: ${testUser.firstName} ${testUser.lastName}`);
    console.log(`  - Email: ${testUser.email}`);
    console.log(`  - Employee Code: ${testUser.functionalCode}`);
    
    // Step 1: Check initial state
    console.log('\n🔍 Step 1: Checking initial database state...');
    const initialUsers = await db.collection('users').countDocuments();
    const initialEmployees = await db.collection('employees').countDocuments();
    console.log(`  - Users: ${initialUsers}`);
    console.log(`  - Employees: ${initialEmployees}`);
    
    // Step 2: Initiate registration
    console.log('\n🚀 Step 2: Initiating employee code registration...');
    try {
      const initiateResponse = await axios.post(`${API_BASE}/website/auth/initiate-registration`, {
        firstName: testUser.firstName,
        lastName: testUser.lastName,
        email: testUser.email,
        functionalCode: testUser.functionalCode
      });
      
      console.log('✅ Registration initiated successfully');
      console.log('  - Response:', initiateResponse.data.message);
    } catch (error) {
      console.error('❌ Registration initiation failed:', error.response?.data || error.message);
      return;
    }
    
    // Step 3: Get OTP from Redis (simulate OTP verification)
    console.log('\n🔐 Step 3: Simulating OTP verification...');
    
    // For testing, we'll directly get the OTP from Redis
    const redis = require('redis');
    const redisClient = redis.createClient({ url: 'redis://localhost:6379' });
    await redisClient.connect();
    
    const otp = await redisClient.get(`employee_code_otp:${testUser.email}`);
    console.log(`  - Retrieved OTP: ${otp}`);
    
    if (!otp) {
      console.error('❌ No OTP found in Redis');
      await redisClient.quit();
      return;
    }
    
    // Step 4: Verify OTP
    console.log('\n✅ Step 4: Verifying OTP...');
    try {
      const verifyResponse = await axios.post(`${API_BASE}/website/auth/verify-otp`, {
        email: testUser.email,
        otp: otp
      });
      
      console.log('✅ OTP verified successfully');
    } catch (error) {
      console.error('❌ OTP verification failed:', error.response?.data || error.message);
      await redisClient.quit();
      return;
    }
    
    // Step 5: Complete registration (this should create both User and Employee)
    console.log('\n🎯 Step 5: Completing registration (User + Employee creation)...');
    try {
      const completeResponse = await axios.post(`${API_BASE}/website/auth/complete-registration`, {
        email: testUser.email,
        password: testUser.password
      });
      
      console.log('✅ Registration completed successfully');
      console.log('  - Access token received:', !!completeResponse.data.accessToken);
      console.log('  - Refresh token received:', !!completeResponse.data.refreshToken);
    } catch (error) {
      console.error('❌ Registration completion failed:', error.response?.data || error.message);
      await redisClient.quit();
      return;
    }
    
    await redisClient.quit();
    
    // Step 6: Verify database changes
    console.log('\n🔍 Step 6: Verifying database changes...');
    
    // Check Users collection
    const finalUsers = await db.collection('users').countDocuments();
    const newUser = await db.collection('users').findOne({ email: testUser.email });
    
    console.log(`  - Total users: ${initialUsers} → ${finalUsers} (${finalUsers > initialUsers ? '✅ +1' : '❌ No change'})`);
    
    if (newUser) {
      console.log('  - New user created:');
      console.log(`    • Name: ${newUser.firstName} ${newUser.lastName}`);
      console.log(`    • Email: ${newUser.email}`);
      console.log(`    • Role: ${newUser.role}`);
      console.log(`    • ID: ${newUser._id}`);
    }
    
    // Check Employees collection
    const finalEmployees = await db.collection('employees').countDocuments();
    const newEmployee = await db.collection('employees').findOne({ email: testUser.email });
    
    console.log(`  - Total employees: ${initialEmployees} → ${finalEmployees} (${finalEmployees > initialEmployees ? '✅ +1' : '❌ No change'})`);
    
    if (newEmployee) {
      console.log('  - New employee created:');
      console.log(`    • Name: ${newEmployee.firstName} ${newEmployee.lastName}`);
      console.log(`    • Email: ${newEmployee.email}`);
      console.log(`    • Employee ID: ${newEmployee.employeeId}`);
      console.log(`    • Position: ${newEmployee.position}`);
      console.log(`    • Department: ${newEmployee.department}`);
      console.log(`    • Status: ${newEmployee.status}`);
      console.log(`    • Linked User ID: ${newEmployee.userId}`);
      console.log(`    • User-Employee Link: ${newUser && newEmployee.userId?.toString() === newUser._id.toString() ? '✅ Correct' : '❌ Mismatch'}`);
    }
    
    // Check employee code usage
    const employeeCode = await db.collection('employeeCodes').findOne({ code: testUser.functionalCode });
    console.log(`  - Employee code ${testUser.functionalCode}: ${employeeCode?.isUsed ? '✅ Marked as used' : '❌ Still unused'}`);
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`  - User Creation: ${newUser ? '✅ Success' : '❌ Failed'}`);
    console.log(`  - Employee Creation: ${newEmployee ? '✅ Success' : '❌ Failed'}`);
    console.log(`  - User-Employee Linking: ${newUser && newEmployee && newEmployee.userId?.toString() === newUser._id.toString() ? '✅ Success' : '❌ Failed'}`);
    console.log(`  - Employee Code Usage: ${employeeCode?.isUsed ? '✅ Success' : '❌ Failed'}`);
    
    const allSuccess = newUser && newEmployee && newEmployee.userId?.toString() === newUser._id.toString() && employeeCode?.isUsed;
    console.log(`\n🎯 Overall Result: ${allSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await client.close();
  }
}

// Install required packages if not present
async function checkDependencies() {
  try {
    require('axios');
    require('redis');
  } catch (error) {
    console.log('📦 Installing required dependencies...');
    const { execSync } = require('child_process');
    execSync('npm install axios redis', { stdio: 'inherit' });
  }
}

checkDependencies().then(() => {
  testRegistrationFlow();
});
