# Technology System

## Overview
The technology module manages all tech stacks the company works with. These technologies are linked to employees and projects to reflect expertise and experience.

## Features
- Admin can:
  - Add new technology with:
    - Name
    - Icon/image (any file format)
    - Proficiency level
    - (Auto-calculated) Number of related projects
  - View all technologies.
  - Edit tech details.
  - Delete technologies.

## Fields
- Name (string)
- Icon (image file)
- Skill Level (0–100%)
- Projects Count (auto-linked from project system)
- CreatedAt, UpdatedAt

## Integration with Other Systems
- **Employee Management System**:
  - Employees can be tagged with one or more technologies.
- **Project Management System**:
  - Each project uses one or more technologies.
  - Count of projects using a tech is automatically calculated.

## Admin Interface
- File upload support for icons.
- Auto-update project count.
- TailwindCSS v4 responsive grid.

---

**Next**: `PROJECTS.md`

