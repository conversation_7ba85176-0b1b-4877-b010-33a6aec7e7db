import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminTasksApi } from '@/lib/api/admin/tasks';
import { Task, CreateTaskDto, UpdateTaskDto, TaskFilters, CreateCommentDto, CreateTimeLogDto } from '@/lib/types/task';
import { toast } from 'react-hot-toast';

// Query keys
export const taskKeys = {
  all: ['tasks'] as const,
  lists: () => [...taskKeys.all, 'list'] as const,
  list: (filters: TaskFilters) => [...taskKeys.lists(), filters] as const,
  details: () => [...taskKeys.all, 'detail'] as const,
  detail: (id: string) => [...taskKeys.details(), id] as const,
  stats: () => [...taskKeys.all, 'stats'] as const,
  analytics: (id: string) => [...taskKeys.all, 'analytics', id] as const,
  subtasks: (parentId: string) => [...taskKeys.all, 'subtasks', parentId] as const,
  templates: () => [...taskKeys.all, 'templates'] as const,
  projectStats: (projectId: string) => [...taskKeys.all, 'project-stats', projectId] as const,
  userStats: (userId: string) => [...taskKeys.all, 'user-stats', userId] as const,
};

// Queries
export const useTasks = (filters: TaskFilters = {}) => {
  return useQuery({
    queryKey: taskKeys.list(filters),
    queryFn: () => adminTasksApi.getAll(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useTask = (id: string) => {
  return useQuery({
    queryKey: taskKeys.detail(id),
    queryFn: () => adminTasksApi.getById(id),
    enabled: !!id,
  });
};

export const useTaskStats = () => {
  return useQuery({
    queryKey: taskKeys.stats(),
    queryFn: () => adminTasksApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useTaskAnalytics = (id: string) => {
  return useQuery({
    queryKey: taskKeys.analytics(id),
    queryFn: () => adminTasksApi.getTaskAnalytics(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useSubtasks = (parentId: string) => {
  return useQuery({
    queryKey: taskKeys.subtasks(parentId),
    queryFn: () => adminTasksApi.getSubtasks(parentId),
    enabled: !!parentId,
  });
};

export const useTaskTemplates = () => {
  return useQuery({
    queryKey: taskKeys.templates(),
    queryFn: () => adminTasksApi.getTemplates(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useProjectTaskStats = (projectId: string) => {
  return useQuery({
    queryKey: taskKeys.projectStats(projectId),
    queryFn: () => adminTasksApi.getProjectTaskStats(projectId),
    enabled: !!projectId,
    staleTime: 5 * 60 * 1000,
  });
};

export const useUserTaskStats = (userId: string) => {
  return useQuery({
    queryKey: taskKeys.userStats(userId),
    queryFn: () => adminTasksApi.getUserTaskStats(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
};

// Mutations
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTaskDto) => adminTasksApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Task created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create task');
    },
  });
};

export const useUpdateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTaskDto }) =>
      adminTasksApi.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Task updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update task');
    },
  });
};

export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminTasksApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Task deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete task');
    },
  });
};

export const useUpdateTaskStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      adminTasksApi.updateStatus(id, status),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Task status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update task status');
    },
  });
};

export const useAssignTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, assignedTo }: { id: string; assignedTo: string }) =>
      adminTasksApi.assign(id, assignedTo),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.id) });
      toast.success('Task assigned successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to assign task');
    },
  });
};

export const useAddTaskComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCommentDto) => adminTasksApi.addComment(data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.taskId) });
      toast.success('Comment added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add comment');
    },
  });
};

export const useUploadTaskAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, file }: { taskId: string; file: File }) =>
      adminTasksApi.uploadAttachment(taskId, file),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.taskId) });
      toast.success('Attachment uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to upload attachment');
    },
  });
};

export const useAddTimeLog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTimeLogDto) => adminTasksApi.addTimeLog(data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.taskId) });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Time log added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add time log');
    },
  });
};

export const useCreateSubtask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ parentId, data }: { parentId: string; data: CreateTaskDto }) =>
      adminTasksApi.createSubtask(parentId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.subtasks(variables.parentId) });
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.parentId) });
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      toast.success('Subtask created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create subtask');
    },
  });
};

export const useBlockTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      adminTasksApi.blockTask(id, reason),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(variables.id) });
      toast.success('Task blocked successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to block task');
    },
  });
};

export const useBulkUpdateTasks = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ ids, action, value }: { ids: string[]; action: 'status' | 'assign'; value: string }) => {
      if (action === 'status') {
        return adminTasksApi.bulkUpdateStatus(ids, value);
      } else if (action === 'assign') {
        return adminTasksApi.bulkAssign(ids, value);
      }
      throw new Error('Invalid action');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
      queryClient.invalidateQueries({ queryKey: taskKeys.stats() });
      toast.success('Tasks updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update tasks');
    },
  });
};
