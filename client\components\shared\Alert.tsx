'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils/cn';
import { IconButton } from '@/components/shared/forms/Button';

interface AlertProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  closable?: boolean;
  onClose?: () => void;
  showIcon?: boolean;
  className?: string;
  actions?: React.ReactNode;
}

const alertStyles = {
  info: {
    container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
    icon: 'text-blue-600 dark:text-blue-400',
    title: 'text-blue-800 dark:text-blue-300',
    message: 'text-blue-700 dark:text-blue-400',
  },
  success: {
    container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
    icon: 'text-green-600 dark:text-green-400',
    title: 'text-green-800 dark:text-green-300',
    message: 'text-green-700 dark:text-green-400',
  },
  warning: {
    container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
    icon: 'text-yellow-600 dark:text-yellow-400',
    title: 'text-yellow-800 dark:text-yellow-300',
    message: 'text-yellow-700 dark:text-yellow-400',
  },
  error: {
    container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
    icon: 'text-red-600 dark:text-red-400',
    title: 'text-red-800 dark:text-red-300',
    message: 'text-red-700 dark:text-red-400',
  },
};

const alertIcons = {
  info: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  success: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  warning: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
  ),
  error: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
};

export const Alert: React.FC<AlertProps> = ({
  type = 'info',
  title,
  message,
  closable = false,
  onClose,
  showIcon = true,
  className,
  actions,
}) => {
  const [visible, setVisible] = useState(true);
  const styles = alertStyles[type];

  const handleClose = () => {
    setVisible(false);
    onClose?.();
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={cn(
      'border rounded-lg p-4',
      styles.container,
      className
    )}>
      <div className="flex">
        {showIcon && (
          <div className={cn('flex-shrink-0', styles.icon)}>
            {alertIcons[type]}
          </div>
        )}
        
        <div className={cn('flex-1', showIcon && 'ml-3')}>
          {title && (
            <h3 className={cn('text-sm font-medium mb-1', styles.title)}>
              {title}
            </h3>
          )}
          <div className={cn('text-sm', styles.message)}>
            {message}
          </div>
          
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>
        
        {closable && (
          <div className="flex-shrink-0 ml-3">
            <IconButton
              variant="ghost"
              size="sm"
              onClick={handleClose}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              }
              className={cn('text-gray-400 hover:text-gray-600', styles.icon)}
            />
          </div>
        )}
      </div>
    </div>
  );
};

interface BannerProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  closable?: boolean;
  onClose?: () => void;
  className?: string;
}

export const Banner: React.FC<BannerProps> = ({
  type = 'info',
  message,
  action,
  closable = false,
  onClose,
  className,
}) => {
  const [visible, setVisible] = useState(true);
  const styles = alertStyles[type];

  const handleClose = () => {
    setVisible(false);
    onClose?.();
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={cn(
      'border-l-4 p-4',
      styles.container,
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={cn('flex-shrink-0', styles.icon)}>
            {alertIcons[type]}
          </div>
          <div className={cn('ml-3 text-sm font-medium', styles.message)}>
            {message}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {action && (
            <button
              onClick={action.onClick}
              className={cn(
                'text-sm font-medium underline hover:no-underline',
                styles.title
              )}
            >
              {action.label}
            </button>
          )}
          
          {closable && (
            <IconButton
              variant="ghost"
              size="sm"
              onClick={handleClose}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              }
              className={cn('text-gray-400 hover:text-gray-600', styles.icon)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

interface ToastProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  duration?: number;
  onClose?: () => void;
  className?: string;
}

export const Toast: React.FC<ToastProps> = ({
  type = 'info',
  title,
  message,
  duration = 5000,
  onClose,
  className,
}) => {
  const [visible, setVisible] = useState(true);
  const styles = alertStyles[type];

  React.useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setVisible(false);
        onClose?.();
      }, duration);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [duration, onClose]);

  const handleClose = () => {
    setVisible(false);
    onClose?.();
  };

  if (!visible) {
    return null;
  }

  return (
    <div className={cn(
      'max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
      className
    )}>
      <div className="p-4">
        <div className="flex items-start">
          <div className={cn('flex-shrink-0', styles.icon)}>
            {alertIcons[type]}
          </div>
          <div className="ml-3 w-0 flex-1 pt-0.5">
            {title && (
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {title}
              </p>
            )}
            <p className={cn(
              'text-sm text-gray-500 dark:text-gray-400',
              title && 'mt-1'
            )}>
              {message}
            </p>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <IconButton
              variant="ghost"
              size="sm"
              onClick={handleClose}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              }
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

interface NotificationProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp?: Date;
  read?: boolean;
  onClick?: () => void;
  onMarkAsRead?: () => void;
  className?: string;
}

export const Notification: React.FC<NotificationProps> = ({
  type = 'info',
  title,
  message,
  timestamp,
  read = false,
  onClick,
  onMarkAsRead,
  className,
}) => {
  const styles = alertStyles[type];

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div
      className={cn(
        'flex items-start p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer',
        !read && 'bg-blue-50 dark:bg-blue-900/10',
        className
      )}
      onClick={onClick}
    >
      <div className={cn('flex-shrink-0 mt-0.5', styles.icon)}>
        {alertIcons[type]}
      </div>
      
      <div className="ml-3 flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <p className={cn(
            'text-sm font-medium',
            read ? 'text-gray-700 dark:text-gray-300' : 'text-gray-900 dark:text-white'
          )}>
            {title}
          </p>
          {!read && (
            <div className="w-2 h-2 bg-blue-600 rounded-full ml-2 mt-1 flex-shrink-0" />
          )}
        </div>
        
        <p className={cn(
          'mt-1 text-sm',
          read ? 'text-gray-500 dark:text-gray-400' : 'text-gray-600 dark:text-gray-300'
        )}>
          {message}
        </p>
        
        <div className="mt-2 flex items-center justify-between">
          {timestamp && (
            <span className="text-xs text-gray-400 dark:text-gray-500">
              {formatTimestamp(timestamp)}
            </span>
          )}
          
          {!read && onMarkAsRead && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onMarkAsRead();
              }}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              Mark as read
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
