import apiClient from '../client';
import { Task, CreateTaskDto, UpdateTaskDto, TaskFilters, CreateCommentDto, CreateTimeLogDto } from '@/lib/types/task';
import { ApiResponse, PaginatedResponse } from '@/lib/types/api';

export const adminTasksApi = {
  // Get all tasks with filters and pagination
  getAll: (params: TaskFilters): Promise<ApiResponse<PaginatedResponse<Task>>> =>
    apiClient.get('/admin/tasks', { params }),

  // Get task by ID
  getById: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.get(`/admin/tasks/${id}`),

  // Create new task
  create: (data: CreateTaskDto): Promise<ApiResponse<Task>> =>
    apiClient.post('/admin/tasks', data),

  // Update task
  update: (id: string, data: UpdateTaskDto): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}`, data),

  // Delete task (soft delete)
  delete: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/tasks/${id}`),

  // Bulk operations
  bulkDelete: (ids: string[]): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/tasks/bulk-delete', { ids }),

  bulkUpdateStatus: (ids: string[], status: string): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/tasks/bulk-update-status', { ids, status }),

  bulkAssign: (ids: string[], assignedTo: string): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/tasks/bulk-assign', { ids, assignedTo }),

  // Task status management
  updateStatus: (id: string, status: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/status`, { status }),

  markComplete: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/complete`),

  markInProgress: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/in-progress`),

  markOnHold: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/on-hold`),

  // Task assignment
  assign: (id: string, assignedTo: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/assign`, { assignedTo }),

  unassign: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/unassign`),

  // Task comments
  addComment: (data: CreateCommentDto): Promise<ApiResponse<Task>> =>
    apiClient.post('/admin/tasks/comments', data),

  updateComment: (commentId: string, content: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/comments/${commentId}`, { content }),

  deleteComment: (commentId: string): Promise<ApiResponse<Task>> =>
    apiClient.delete(`/admin/tasks/comments/${commentId}`),

  // Task attachments
  uploadAttachment: (taskId: string, file: File): Promise<ApiResponse<Task>> => {
    const formData = new FormData();
    formData.append('file', file);
    return apiClient.post(`/admin/tasks/${taskId}/attachments`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  deleteAttachment: (taskId: string, attachmentId: string): Promise<ApiResponse<Task>> =>
    apiClient.delete(`/admin/tasks/${taskId}/attachments/${attachmentId}`),

  downloadAttachment: (taskId: string, attachmentId: string): Promise<Blob> =>
    apiClient.get(`/admin/tasks/${taskId}/attachments/${attachmentId}/download`, {
      responseType: 'blob'
    }),

  // Task time logging
  addTimeLog: (data: CreateTimeLogDto): Promise<ApiResponse<Task>> =>
    apiClient.post('/admin/tasks/time-logs', data),

  updateTimeLog: (timeLogId: string, data: Partial<CreateTimeLogDto>): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/time-logs/${timeLogId}`, data),

  deleteTimeLog: (timeLogId: string): Promise<ApiResponse<Task>> =>
    apiClient.delete(`/admin/tasks/time-logs/${timeLogId}`),

  // Task dependencies
  addDependency: (taskId: string, dependencyId: string): Promise<ApiResponse<Task>> =>
    apiClient.post(`/admin/tasks/${taskId}/dependencies`, { dependencyId }),

  removeDependency: (taskId: string, dependencyId: string): Promise<ApiResponse<Task>> =>
    apiClient.delete(`/admin/tasks/${taskId}/dependencies/${dependencyId}`),

  // Task subtasks
  createSubtask: (parentId: string, data: CreateTaskDto): Promise<ApiResponse<Task>> =>
    apiClient.post(`/admin/tasks/${parentId}/subtasks`, data),

  getSubtasks: (parentId: string): Promise<ApiResponse<Task[]>> =>
    apiClient.get(`/admin/tasks/${parentId}/subtasks`),

  // Task blocking
  blockTask: (id: string, reason: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/block`, { reason }),

  unblockTask: (id: string): Promise<ApiResponse<Task>> =>
    apiClient.patch(`/admin/tasks/${id}/unblock`),

  // Task analytics
  getStats: (): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/tasks/stats'),

  getTaskAnalytics: (id: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/tasks/${id}/analytics`),

  getProjectTaskStats: (projectId: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/tasks/project/${projectId}/stats`),

  getUserTaskStats: (userId: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/tasks/user/${userId}/stats`),

  // Task reports
  getTimeReport: (startDate?: Date, endDate?: Date, userId?: string): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/tasks/time-report', { 
      params: { startDate, endDate, userId }
    }),

  getProductivityReport: (userId?: string, period?: string): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/tasks/productivity-report', { 
      params: { userId, period }
    }),

  // Task export
  export: (format: 'csv' | 'excel', filters?: TaskFilters): Promise<Blob> =>
    apiClient.get('/admin/tasks/export', { 
      params: { format, ...filters },
      responseType: 'blob'
    }),

  // Task templates
  getTemplates: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/admin/tasks/templates'),

  createFromTemplate: (templateId: string, data: any): Promise<ApiResponse<Task>> =>
    apiClient.post('/admin/tasks/from-template', { templateId, ...data }),

  // Task notifications
  getNotifications: (taskId: string): Promise<ApiResponse<any[]>> =>
    apiClient.get(`/admin/tasks/${taskId}/notifications`),

  markNotificationRead: (notificationId: string): Promise<ApiResponse<void>> =>
    apiClient.patch(`/admin/tasks/notifications/${notificationId}/read`),
};
