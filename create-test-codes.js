const { MongoClient } = require('mongodb');

async function createTestEmployeeCodes() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('silla_link');
    
    console.log('🔧 Creating test employee codes...\n');
    
    const testCodes = [
      {
        code: 'TEST001',
        description: 'Test Employee Code for Software Developer',
        role: 'EMPLOYEE',
        isUsed: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        code: 'TEST002',
        description: 'Test Employee Code for Team Leader',
        role: 'TEAM_LEADER',
        isUsed: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        code: 'TEST003',
        description: 'Test Employee Code for Project Manager',
        role: 'PROJECT_MANAGER',
        isUsed: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    // Check if codes already exist
    for (const testCode of testCodes) {
      const existing = await db.collection('employeeCodes').findOne({ code: testCode.code });
      if (!existing) {
        await db.collection('employeeCodes').insertOne(testCode);
        console.log(`✅ Created employee code: ${testCode.code} (${testCode.role})`);
      } else {
        console.log(`⚠️ Employee code already exists: ${testCode.code}`);
      }
    }
    
    console.log('\n🎫 Available Employee Codes:');
    const codes = await db.collection('employeeCodes').find({
      isUsed: false,
      isActive: true
    }).toArray();
    
    codes.forEach(code => {
      console.log(`  - ${code.code} (${code.role}) - ${code.description}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

createTestEmployeeCodes();
