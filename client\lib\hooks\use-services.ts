import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminServicesApi } from '@/lib/api/admin/services';
import { Service, CreateServiceDto, UpdateServiceDto, ServiceFilters, ServiceInquiry } from '@/lib/types/service';
import { toast } from 'react-hot-toast';

// Query keys
export const serviceKeys = {
  all: ['services'] as const,
  lists: () => [...serviceKeys.all, 'list'] as const,
  list: (filters: ServiceFilters) => [...serviceKeys.lists(), filters] as const,
  details: () => [...serviceKeys.all, 'detail'] as const,
  detail: (id: string) => [...serviceKeys.details(), id] as const,
  stats: () => [...serviceKeys.all, 'stats'] as const,
  analytics: (id: string) => [...serviceKeys.all, 'analytics', id] as const,
  inquiries: () => [...serviceKeys.all, 'inquiries'] as const,
  inquiry: (id: string) => [...serviceKeys.inquiries(), id] as const,
  templates: () => [...serviceKeys.all, 'templates'] as const,
};

// Queries
export const useServices = (filters: ServiceFilters = {}) => {
  return useQuery({
    queryKey: serviceKeys.list(filters),
    queryFn: () => adminServicesApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useService = (id: string) => {
  return useQuery({
    queryKey: serviceKeys.detail(id),
    queryFn: () => adminServicesApi.getById(id),
    enabled: !!id,
  });
};

export const useServiceStats = () => {
  return useQuery({
    queryKey: serviceKeys.stats(),
    queryFn: () => adminServicesApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useServiceAnalytics = (id: string) => {
  return useQuery({
    queryKey: serviceKeys.analytics(id),
    queryFn: () => adminServicesApi.getServiceAnalytics(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useServiceInquiries = (serviceId?: string) => {
  return useQuery({
    queryKey: [...serviceKeys.inquiries(), serviceId],
    queryFn: () => adminServicesApi.getInquiries(serviceId),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useServiceInquiry = (id: string) => {
  return useQuery({
    queryKey: serviceKeys.inquiry(id),
    queryFn: () => adminServicesApi.getInquiryById(id),
    enabled: !!id,
  });
};

export const useServiceTemplates = () => {
  return useQuery({
    queryKey: serviceKeys.templates(),
    queryFn: () => adminServicesApi.getTemplates(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Mutations
export const useCreateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateServiceDto) => adminServicesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.stats() });
      toast.success('Service created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create service');
    },
  });
};

export const useUpdateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateServiceDto }) =>
      adminServicesApi.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: serviceKeys.stats() });
      toast.success('Service updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update service');
    },
  });
};

export const useDeleteService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminServicesApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.stats() });
      toast.success('Service deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete service');
    },
  });
};

export const useToggleServiceStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, action }: { id: string; action: 'activate' | 'deactivate' | 'feature' | 'unfeature' }) => {
      switch (action) {
        case 'activate':
          return adminServicesApi.activate(id);
        case 'deactivate':
          return adminServicesApi.deactivate(id);
        case 'feature':
          return adminServicesApi.feature(id);
        case 'unfeature':
          return adminServicesApi.unfeature(id);
        default:
          throw new Error('Invalid action');
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: serviceKeys.stats() });
      toast.success('Service status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update service status');
    },
  });
};

export const useUploadServiceImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceId, file }: { serviceId: string; file: File }) =>
      adminServicesApi.uploadImage(serviceId, file),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.serviceId) });
      toast.success('Image uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to upload image');
    },
  });
};

export const useManageServicePackage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      serviceId, 
      packageId, 
      packageData, 
      action 
    }: { 
      serviceId: string; 
      packageId?: string; 
      packageData?: any; 
      action: 'add' | 'update' | 'delete' 
    }) => {
      switch (action) {
        case 'add':
          return adminServicesApi.addPackage(serviceId, packageData);
        case 'update':
          return adminServicesApi.updatePackage(serviceId, packageId!, packageData);
        case 'delete':
          return adminServicesApi.deletePackage(serviceId, packageId!);
        default:
          throw new Error('Invalid action');
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.serviceId) });
      toast.success('Package updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update package');
    },
  });
};

export const useManageServiceFeature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      serviceId, 
      featureId, 
      featureData, 
      action 
    }: { 
      serviceId: string; 
      featureId?: string; 
      featureData?: any; 
      action: 'add' | 'update' | 'delete' 
    }) => {
      switch (action) {
        case 'add':
          return adminServicesApi.addFeature(serviceId, featureData);
        case 'update':
          return adminServicesApi.updateFeature(serviceId, featureId!, featureData);
        case 'delete':
          return adminServicesApi.deleteFeature(serviceId, featureId!);
        default:
          throw new Error('Invalid action');
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.serviceId) });
      toast.success('Feature updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update feature');
    },
  });
};

export const useUpdateInquiryStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      adminServicesApi.updateInquiryStatus(id, status),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.inquiries() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.inquiry(variables.id) });
      toast.success('Inquiry status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update inquiry status');
    },
  });
};

export const useAssignInquiry = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, assignedTo }: { id: string; assignedTo: string }) =>
      adminServicesApi.assignInquiry(id, assignedTo),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.inquiries() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.inquiry(variables.id) });
      toast.success('Inquiry assigned successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to assign inquiry');
    },
  });
};

export const useBulkDeleteServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => adminServicesApi.bulkDelete(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.stats() });
      toast.success('Services deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete services');
    },
  });
};
