const { MongoClient } = require('mongodb');

async function debugEmployeeCode() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('silla_link');
    
    console.log('🔍 Debugging Employee Code Validation...\n');
    
    // Check the specific code TEST001
    const code = await db.collection('employeeCodes').findOne({ code: 'TEST001' });
    
    if (code) {
      console.log('📋 Employee Code Details:');
      console.log('  - Code:', code.code);
      console.log('  - Role:', code.role);
      console.log('  - Description:', code.description);
      console.log('  - isUsed:', code.isUsed);
      console.log('  - isActive:', code.isActive);
      console.log('  - createdBy:', code.createdBy);
      console.log('  - expiresAt:', code.expiresAt);
      console.log('  - usedBy:', code.usedBy);
      console.log('  - usedAt:', code.usedAt);
      
      // Check validation conditions
      console.log('\n✅ Validation Checks:');
      console.log('  - Code exists:', !!code);
      console.log('  - Not used:', !code.isUsed);
      console.log('  - Is active:', code.isActive !== false);
      console.log('  - Not expired:', !code.expiresAt || new Date(code.expiresAt) > new Date());
      
      const isValid = code && !code.isUsed && code.isActive !== false && (!code.expiresAt || new Date(code.expiresAt) > new Date());
      console.log('  - Overall valid:', isValid);
      
      if (!isValid) {
        console.log('\n❌ Code validation failed because:');
        if (!code) console.log('  - Code does not exist');
        if (code.isUsed) console.log('  - Code is already used');
        if (code.isActive === false) console.log('  - Code is not active');
        if (code.expiresAt && new Date(code.expiresAt) <= new Date()) console.log('  - Code has expired');
      }
    } else {
      console.log('❌ Employee code TEST001 not found in database');
    }
    
    // Check all employee codes
    console.log('\n📊 All Employee Codes:');
    const allCodes = await db.collection('employeeCodes').find({}).toArray();
    allCodes.forEach((code, index) => {
      const isValid = code && !code.isUsed && code.isActive !== false && (!code.expiresAt || new Date(code.expiresAt) > new Date());
      console.log(`  ${index + 1}. ${code.code} (${code.role}) - Used: ${code.isUsed}, Active: ${code.isActive}, Valid: ${isValid}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

debugEmployeeCode();
