'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  FiHome,
  FiUsers,
  FiFolderPlus,
  FiCode,
  FiActivity,
  FiSettings,
  FiShield,
  FiClock,
  FiBar<PERSON>hart
} from 'react-icons/fi';
import { cn } from '@/lib/utils/cn';

interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: number | string;
  requiredRoles?: string[];
  children?: NavigationItem[];
}

interface AdminNavigationProps {
  userRole?: string;
  isCollapsed?: boolean;
  className?: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/admin/dashboard',
    icon: FiHome,
    description: 'Overview and analytics',
    requiredRoles: ['ADMIN', 'PROJECT_MANAGER', 'TEAM_LEADER']
  },
  {
    id: 'employees',
    label: 'Employees',
    href: '/admin/employees',
    icon: FiUsers,
    description: 'Employee management',
    requiredRoles: ['ADMIN', 'PROJECT_MANAGER']
  },
  {
    id: 'projects',
    label: 'Projects',
    href: '/admin/projects',
    icon: FiFolderPlus,
    description: 'Project management',
    requiredRoles: ['ADMIN', 'PROJECT_MANAGER', 'TEAM_LEADER']
  },
  {
    id: 'tasks',
    label: 'Tasks',
    href: '/admin/tasks',
    icon: FiClock,
    description: 'Task management',
    requiredRoles: ['ADMIN', 'PROJECT_MANAGER', 'TEAM_LEADER']
  },
  {
    id: 'technologies',
    label: 'Technologies',
    href: '/admin/technologies',
    icon: FiCode,
    description: 'Tech stack management',
    requiredRoles: ['ADMIN']
  },
  {
    id: 'employee-codes',
    label: 'Employee Codes',
    href: '/admin/employee-codes',
    icon: FiShield,
    description: 'Registration codes',
    badge: 'Admin',
    requiredRoles: ['ADMIN']
  },
  {
    id: 'analytics',
    label: 'Analytics',
    href: '/admin/analytics',
    icon: FiBarChart,
    description: 'Reports and insights',
    requiredRoles: ['ADMIN', 'PROJECT_MANAGER']
  },
  {
    id: 'health',
    label: 'System Health',
    href: '/admin/health',
    icon: FiActivity,
    description: 'System monitoring',
    requiredRoles: ['ADMIN']
  },
  {
    id: 'settings',
    label: 'Settings',
    href: '/admin/settings',
    icon: FiSettings,
    description: 'System configuration',
    requiredRoles: ['ADMIN']
  }
];

export const AdminNavigation: React.FC<AdminNavigationProps> = ({
  userRole = 'ADMIN',
  isCollapsed = false,
  className
}) => {
  const pathname = usePathname();

  // Normalize user role to uppercase for consistency
  const normalizedUserRole = userRole?.toUpperCase() || 'ADMIN';

  // Filter navigation items based on user role
  const accessibleItems = navigationItems.filter(item => {
    if (!item.requiredRoles) return true;
    return item.requiredRoles.includes(normalizedUserRole);
  });

  const isActiveRoute = (href: string) => {
    if (href === '/admin/dashboard') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className={cn('space-y-2', className)}>
      {accessibleItems.map((item, index) => {
        const Icon = item.icon;
        const isActive = isActiveRoute(item.href);

        // Safety check for undefined icon
        if (!Icon) {
          console.error(`Icon is undefined for navigation item: ${item.id}`);
          return null;
        }

        return (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <Link
              href={item.href}
              className={cn(
                'group relative flex items-center px-4 py-3 rounded-xl transition-all duration-200',
                'hover:bg-dark-700/50',
                isActive
                  ? 'bg-gradient-to-r from-primary-500/20 to-accent-purple/20 text-primary-400 border border-primary-500/30'
                  : 'text-primary-300 hover:text-white'
              )}
            >
              {/* Active indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary-500 to-accent-purple rounded-r-full"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}

              {/* Icon */}
              <div className={cn(
                'flex items-center justify-center w-10 h-10 rounded-lg transition-colors',
                isActive
                  ? 'bg-gradient-to-br from-primary-500/30 to-accent-purple/30'
                  : 'bg-dark-700 group-hover:bg-dark-600'
              )}>
                <Icon className={cn(
                  'w-5 h-5 transition-colors',
                  isActive
                    ? 'text-primary-400'
                    : 'text-primary-300 group-hover:text-white'
                )} />
              </div>

              {/* Label and description */}
              {!isCollapsed && (
                <div className="flex-1 ml-3 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className={cn(
                      'text-sm font-medium truncate',
                      isActive
                        ? 'text-primary-400'
                        : 'text-white'
                    )}>
                      {item.label}
                    </p>
                    {item.badge && (
                      <span className={cn(
                        'ml-2 px-2 py-1 text-xs font-medium rounded-full',
                        isActive
                          ? 'bg-primary-500/30 text-primary-300'
                          : 'bg-dark-700 text-primary-300'
                      )}>
                        {item.badge}
                      </span>
                    )}
                  </div>
                  {item.description && (
                    <p className="text-xs text-primary-300/70 truncate mt-0.5">
                      {item.description}
                    </p>
                  )}
                </div>
              )}

              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-2 px-3 py-2 bg-dark-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50 border border-dark-700">
                  <div className="font-medium">{item.label}</div>
                  {item.description && (
                    <div className="text-xs text-primary-300 mt-1">{item.description}</div>
                  )}
                  <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-dark-900 rotate-45 border-l border-b border-dark-700"></div>
                </div>
              )}
            </Link>
          </motion.div>
        );
      })}

      {/* Role-based access notice */}
      {!isCollapsed && accessibleItems.length < navigationItems.length && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="px-4 py-3 mt-4"
        >
          <div className="text-xs text-primary-300 bg-dark-700/50 rounded-lg p-3 border border-dark-600">
            <div className="flex items-center space-x-2">
              <FiShield className="w-3 h-3" />
              <span>Role: {normalizedUserRole}</span>
            </div>
            <div className="mt-1">
              {accessibleItems.length} of {navigationItems.length} modules accessible
            </div>
          </div>
        </motion.div>
      )}
    </nav>
  );
};
