import { Schema, <PERSON>p, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from 'mongoose';
import { UserRole } from "../../interfaces/user-role.enum";
import {RefreshToken, RefreshTokenSchema} from "./refresh-token.schema";

export type UserDocument = User & Document;

@Schema({ timestamps: true, collection: 'users' })
export class User {
    @Prop({ required: true, unique: true, lowercase: true, trim: true })
    email: string;

    @Prop({ required: true, select: false })
    password: string;

    @Prop({ required: true, trim: true, maxlength: 50 })
    firstName: string;

    @Prop({ required: true, trim: true, maxlength: 50 })
    lastName: string;

    @Prop({ type: String, default: UserRole.USER, enum: UserRole })
    role: UserRole;

    @Prop({ type: String, required: false, index: true, trim: true, uppercase: true })
    functionalCode?: string;

    @Prop({ type: String, unique: true, sparse: true, trim: true })
    phone?: string;

    @Prop({type: [RefreshTokenSchema], default: []})
    refreshToken?: RefreshToken[]

    @Prop({ default: true })
    isActive: boolean;

    @Prop({ default: false })
    isEmailVerified: boolean;

    @Prop({ type: Date, default: null })
    emailVerifiedAt?: Date;

    @Prop({ type: Date, default: null })
    lastLoginAt?: Date;

    @Prop({ type: String, trim: true, maxlength: 255 })
    profileImage?: string;

    @Prop({ type: Date, default: null })
    deletedAt?: Date;

    @Prop({ default: false })
    isDeleted: boolean;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Indexes for better performance
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ functionalCode: 1 });
UserSchema.index({ role: 1, isActive: 1 });
UserSchema.index({ isDeleted: 1, deletedAt: 1 });
UserSchema.index({ phone: 1 }, { sparse: true });

// Remove password from query results
UserSchema.post("find", (docs: any[]) => {
    docs.map((doc: any) => {
        delete doc.password;
    })
});

UserSchema.post("findOne", (doc: any) => {
    if (doc) {
        delete doc.password;
    }
});
