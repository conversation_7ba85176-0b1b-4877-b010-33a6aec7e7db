import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminEmployeeCodesApi } from '@/lib/api/admin/employeeCodes';
import { EmployeeCode, CreateEmployeeCodeDto, UpdateEmployeeCodeDto, EmployeeCodeFilters } from '@/lib/types/employee-code';
import { toast } from 'react-hot-toast';

// Query keys
export const employeeCodeKeys = {
  all: ['employee-codes'] as const,
  lists: () => [...employeeCodeKeys.all, 'list'] as const,
  list: (filters: EmployeeCodeFilters) => [...employeeCodeKeys.lists(), filters] as const,
  details: () => [...employeeCodeKeys.all, 'detail'] as const,
  detail: (id: string) => [...employeeCodeKeys.details(), id] as const,
  stats: () => [...employeeCodeKeys.all, 'stats'] as const,
};

// Queries
export const useEmployeeCodes = (filters: EmployeeCodeFilters = {}) => {
  return useQuery({
    queryKey: employeeCodeKeys.list(filters),
    queryFn: () => adminEmployeeCodesApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useEmployeeCode = (id: string) => {
  return useQuery({
    queryKey: employeeCodeKeys.detail(id),
    queryFn: () => adminEmployeeCodesApi.getById(id),
    enabled: !!id,
  });
};

export const useEmployeeCodeStats = () => {
  return useQuery({
    queryKey: employeeCodeKeys.stats(),
    queryFn: () => adminEmployeeCodesApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Mutations
export const useCreateEmployeeCode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmployeeCodeDto) => adminEmployeeCodesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee code created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create employee code');
    },
  });
};

export const useUpdateEmployeeCode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmployeeCodeDto }) =>
      adminEmployeeCodesApi.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee code updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update employee code');
    },
  });
};

export const useDeleteEmployeeCode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminEmployeeCodesApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee code deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete employee code');
    },
  });
};

export const useToggleEmployeeCodeStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, action }: { id: string; action: 'activate' | 'deactivate' }) => {
      if (action === 'activate') {
        return adminEmployeeCodesApi.activate(id);
      } else {
        return adminEmployeeCodesApi.deactivate(id);
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee code status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update employee code status');
    },
  });
};

export const useBulkGenerateEmployeeCodes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => adminEmployeeCodesApi.bulkGenerate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee codes generated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to generate employee codes');
    },
  });
};

export const useBulkUpdateEmployeeCodes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => adminEmployeeCodesApi.bulkUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee codes updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update employee codes');
    },
  });
};

export const useBulkDeleteEmployeeCodes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => adminEmployeeCodesApi.bulkDelete(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: employeeCodeKeys.stats() });
      toast.success('Employee codes deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete employee codes');
    },
  });
};
