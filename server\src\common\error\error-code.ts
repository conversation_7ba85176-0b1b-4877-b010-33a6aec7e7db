export enum ErrorCode {
  INVALID_EMPLOYEE_CODE = 4015,

  // auth-system (4000-4999)
  OTP_EXPIRED = 4001,
  INVALID_OTP = 4002,
  INVALID_CREDENTIALS = 4003,
  INVALID_RESET_TOKEN = 4004,
  OTP_VERIFICATION_FAILED = 4005,
  EXPIRED_ACCESS_TOKEN = 4006,
  EXPIRED_REFRESH_TOKEN = 4007,
  USER_NOT_ALLOW = 4008,
  INVALID_TOKEN =  4009,
  REFRESH_TOKEN_NOT_IN_REDIS = 4010,
  SIGN_IN_EMAIL_NOT_IN_REDIS = 4011,
  EMAIL_NOT_VERIFIED = 4012,
  REGISTRATION_DATA_EXPIRED = 4013,
  TOO_MANY_ATTEMPTS = 4014,

  // user-management (2000-2999)
  USER_NOT_FOUND = 2001,
  USER_ALREADY_EXISTS = 2002,
  PHONE_ALREADY_EXISTS = 2003,

  // employee-management (3000-3999)
  EMPLOYEE_NOT_FOUND = 3001,
  EMPLOYEE_ALREADY_EXISTS = 3002,
  <PERSON>MPLOYEE_EMAIL_ALREADY_EXISTS = 3003,
  INVALID_PROFICIENCY_LEVEL = 3004,
  EMPLOYEE_IN_USE = 3005,
  INVALID_EMPLOYEE_STATUS = 3006,
  INVALID_DEPARTMENT = 3007,
  EMPLOYEE_CANNOT_BE_DELETED = 3008,
  EMPLOYEE_ALREADY_LINKED = 3009,
  USER_ALREADY_LINKED = 3010,

  // project-management (5000-5999)
  PROJECT_NOT_FOUND = 5001,
  PROJECT_ALREADY_EXISTS = 5002,
  INVALID_PROJECT_DATA = 5003,
  UNAUTHORIZED_ACCESS = 5004,
  MEMBER_ALREADY_EXISTS = 5005,
  MEMBER_NOT_FOUND = 5006,

  // service-management (7000-7999)
  SERVICE_NOT_FOUND = 7001,
  SERVICE_ALREADY_EXISTS = 7002,

  // technology-management (6000-6999)
  TECHNOLOGY_NOT_FOUND = 6001,
  TECHNOLOGY_ALREADY_EXISTS = 6002,
  TECHNOLOGY_IN_USE = 6003,

  // email (60000-60999)
  MAIL_ERROR =  60001,

  // validation (70000-70999)
  VALIDATION_ERROR = 70000

}