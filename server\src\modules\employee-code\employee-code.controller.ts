import { Controller, Post, Body, Get, Param, Patch, Request, UseGuards, HttpStatus, HttpCode, Res, Query, SetMetadata } from '@nestjs/common';
import { Response } from 'express';
import { EmployeeCodeService } from './employee-code.service';
import { CreateEmployeeCodeDto } from './dto/create-employee-code.dto';
import { JwtAuthGuard } from '@Package/auth/guards';
import { RoleGuard } from '@Package/auth/guards/role.guard';
import { Roles } from '@Package/auth/decorators/roles.decorator';
import { UserRole } from '@Modules/user-management';

@Controller('admin/employee-codes')
@UseGuards(JwtAuthGuard, RoleGuard)
@Roles(UserRole.ADMIN)
export class EmployeeCodeController {
  constructor(private readonly service: EmployeeCodeService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() dto: CreateEmployeeCodeDto, @Request() req, @Res() res: Response) {
    try {
      const result = await this.service.createCode(dto, req.user.id);
      return res.status(HttpStatus.CREATED).json({
        success: true,
        data: result,
        message: 'Employee code created successfully'
      });
    } catch (error) {
      return res.status(HttpStatus.CREATED).json({
        success: false,
        data: null,
        message: error.message || 'Failed to create employee code'
      });
    }
  }

  @Get()
  async findAll(@Query() filters: any) {
    try {
      const result = await this.service.findAll(filters);
      return {
        success: true,
        data: result,
        message: 'Employee codes retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || 'Failed to retrieve employee codes'
      };
    }
  }

  @Get('unused')
  async unused() {
    try {
      const result = await this.service.findAllUnusedCodes();
      return {
        success: true,
        data: result,
        message: 'Unused employee codes retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || 'Failed to retrieve unused employee codes'
      };
    }
  }

  // Temporary endpoint without auth for testing
  @Get('unused-test')
  @SetMetadata('skipAuth', true)
  async unusedTest() {
    try {
      const result = await this.service.findAllUnusedCodes();
      return {
        success: true,
        data: result,
        message: 'Unused employee codes retrieved successfully (test endpoint)'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || 'Failed to retrieve unused employee codes'
      };
    }
  }

  // Temporary fix endpoint for employee codes without createdBy
  @Post('fix-created-by')
  @SetMetadata('skipAuth', true)
  async fixCreatedBy() {
    try {
      const result = await this.service.fixEmployeeCodesCreatedBy();
      return {
        success: true,
        data: result,
        message: 'Employee codes fixed successfully'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Failed to fix employee codes'
      };
    }
  }

  @Get('check/:code')
  async check(@Param('code') code: string) {
    return this.service.checkCode(code);
  }

  @Patch('use/:code')
  async markAsUsed(@Param('code') code: string) {
    return this.service.markCodeAsUsed(code);
  }

  @Get(':id')
  async findById(@Param('id') id: string) {
    try {
      const result = await this.service.findById(id);
      return {
        success: true,
        data: result,
        message: 'Employee code retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || 'Employee code not found'
      };
    }
  }

  // Test endpoint without authentication to check database
  @Get('test/all')
  async testGetAll() {
    try {
      const allCodes = await this.service.findAllCodes();
      const unusedCodes = await this.service.findAllUnusedCodes();
      return {
        success: true,
        totalCount: allCodes.length,
        unusedCount: unusedCodes.length,
        allCodes: allCodes,
        unusedCodes: unusedCodes,
        message: `Found ${allCodes.length} total codes, ${unusedCodes.length} unused`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Error fetching codes'
      };
    }
  }
}
