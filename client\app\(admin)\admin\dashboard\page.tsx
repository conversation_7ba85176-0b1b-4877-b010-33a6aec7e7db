'use client';

import { useAuth } from '@/lib/hooks/use-auth';
import { GlassCard } from '@/components/ui/glass-card';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import {
  FiUsers,
  FiFolderPlus,
  FiCode,
  FiGlobe,
  FiActivity,
  FiSettings,
  FiBarChart,
  FiClock,
  FiTrendingUp,
  FiShield
} from 'react-icons/fi';
import { Card, CardHeader, CardBody } from '@/components/shared/Card';
import { StatCard, MetricCard } from '@/components/shared/Card';
import { Alert } from '@/components/shared/Alert';
import { Spinner } from '@/components/shared/Spinner';

interface DashboardStats {
  totalEmployees: number;
  activeProjects: number;
  totalTechnologies: number;
  totalServices: number;
  systemHealth: string;
  recentActivity: number;
}

interface DashboardCard {
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  count: string | number;
  requiredRole?: string[];
  badge?: string;
}

export default function AdminDashboard() {
  const { user, isLoading } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setStatsLoading(true);
        // TODO: Replace with actual API calls
        // const response = await fetch('/api/admin/dashboard/stats');
        // const data = await response.json();

        // Mock data for now
        const mockStats: DashboardStats = {
          totalEmployees: 156,
          activeProjects: 23,
          totalTechnologies: 45,
          totalServices: 12,
          systemHealth: '99.9%',
          recentActivity: 47
        };

        setStats(mockStats);
      } catch (err) {
        setError('Failed to load dashboard statistics');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStats();
  }, []);

  const dashboardCards: DashboardCard[] = [
    {
      title: 'Employee Management',
      description: 'Manage employee profiles and data',
      href: '/admin/employees',
      icon: FiUsers,
      color: 'from-blue-500 to-blue-600',
      count: stats?.totalEmployees || 0,
      requiredRole: ['ADMIN', 'PROJECT_MANAGER'],
      badge: 'Core'
    },
    {
      title: 'Project Management',
      description: 'Manage projects and assignments',
      href: '/admin/projects',
      icon: FiFolderPlus,
      color: 'from-purple-500 to-purple-600',
      count: stats?.activeProjects || 0,
      requiredRole: ['ADMIN', 'PROJECT_MANAGER', 'TEAM_LEADER']
    },
    {
      title: 'Task Management',
      description: 'Track and manage project tasks',
      href: '/admin/tasks',
      icon: FiClock,
      color: 'from-green-500 to-green-600',
      count: stats?.recentActivity || 0,
      requiredRole: ['ADMIN', 'PROJECT_MANAGER', 'TEAM_LEADER']
    },
    {
      title: 'Technology Stack',
      description: 'Manage technologies and skills',
      href: '/admin/technologies',
      icon: FiCode,
      color: 'from-yellow-500 to-yellow-600',
      count: stats?.totalTechnologies || 0,
      requiredRole: ['ADMIN']
    },
    {
      title: 'Employee Codes',
      description: 'Generate and manage registration codes',
      href: '/admin/employee-codes',
      icon: FiShield,
      color: 'from-indigo-500 to-indigo-600',
      count: 'Active',
      requiredRole: ['ADMIN'],
      badge: 'Admin Only'
    },
    {
      title: 'System Health',
      description: 'Monitor system performance',
      href: '/admin/health',
      icon: FiActivity,
      color: 'from-red-500 to-red-600',
      count: stats?.systemHealth || '0%',
      requiredRole: ['ADMIN']
    }
  ];

  // Filter cards based on user role
  const accessibleCards = dashboardCards.filter(card => {
    if (!card.requiredRole) return true;
    return card.requiredRole.includes(user?.role || '');
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-100 mb-2">Admin Dashboard</h1>
            <p className="text-gray-300">
              Welcome back, {user?.name || 'Administrator'} • Role: {user?.role || 'Admin'}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-400">Last updated</p>
              <p className="text-sm font-medium text-gray-200">
                {new Date().toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Error Alert */}
      {error && (
        <Alert
          type="error"
          message={error}
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* Quick Stats */}
      {stats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <StatCard
            title="Total Employees"
            value={stats.totalEmployees}
            icon={<FiUsers className="w-5 h-5" />}
            change={{ value: 12, type: 'increase' }}
          />
          <StatCard
            title="Active Projects"
            value={stats.activeProjects}
            icon={<FiFolderPlus className="w-5 h-5" />}
            change={{ value: 8, type: 'increase' }}
          />
          <StatCard
            title="Technologies"
            value={stats.totalTechnologies}
            icon={<FiCode className="w-5 h-5" />}
          />
          <StatCard
            title="System Health"
            value={stats.systemHealth}
            icon={<FiActivity className="w-5 h-5" />}
          />
        </motion.div>
      )}

      {/* Management Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsLoading ? (
          // Loading skeleton
          Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="p-6">
              <div className="animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-xl"></div>
                  <div className="text-right">
                    <div className="w-16 h-6 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
                    <div className="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="w-32 h-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  <div className="w-full h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          accessibleCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <motion.div
                key={card.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Link href={card.href}>
                  <GlassCard
                    variant="secondary"
                    className="group cursor-pointer hover:scale-105 transition-all duration-300 bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 shadow-lg hover:shadow-xl hover:bg-slate-800/80"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-12 h-12 bg-gradient-to-br ${card.color} rounded-xl flex items-center justify-center shadow-lg`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-100">{card.count}</div>
                        <div className="text-xs text-gray-400">
                          {typeof card.count === 'string' ? 'Status' : 'Total'}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-100 group-hover:text-purple-300 transition-colors">
                          {card.title}
                        </h3>
                        {card.badge && (
                          <span className="px-2 py-1 text-xs font-medium bg-purple-500/20 text-purple-300 rounded-full">
                            {card.badge}
                          </span>
                        )}
                      </div>
                      <p className="text-gray-300 text-sm">
                        {card.description}
                      </p>
                    </div>

                    <div className="mt-4 pt-4 border-t border-slate-700/50">
                      <div className="flex items-center text-purple-400 text-sm group-hover:text-purple-300 transition-colors font-medium">
                        <span>Manage →</span>
                      </div>
                    </div>
                  </GlassCard>
                </Link>
              </motion.div>
            );
          })
        )}
      </div>

      {/* Role-based Access Notice */}
      {accessibleCards.length < dashboardCards.length && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <Alert
            type="info"
            message={`Showing ${accessibleCards.length} of ${dashboardCards.length} available modules based on your role (${user?.role})`}
          />
        </motion.div>
      )}
    </div>
  );
}
