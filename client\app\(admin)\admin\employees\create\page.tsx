'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiUsers, FiSave, FiX } from 'react-icons/fi';
import { useRouter } from 'next/navigation';
import { useCreateEmployee } from '@/lib/hooks/use-employees';
import { useQueryClient } from '@tanstack/react-query';
import { employeeKeys } from '@/lib/hooks/use-employees';
import { employeesApi } from '@/lib/api/admin/employees';
import { extractErrorMessage } from '@/lib/utils/error-handler';
import { CreateEmployeeData, EmployeeDepartment, EmployeeStatus, EmploymentType } from '@/lib/types/employee';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/shared/forms/Button';
import { GlassCard } from '@/components/ui/glass-card';
import Link from 'next/link';

// Import the form component
import { DynamicEmployeeForm } from '../components/DynamicEmployeeForm';

export default function CreateEmployeePage() {
  const router = useRouter();
  const createEmployeeMutation = useCreateEmployee();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: CreateEmployeeData & {
    linkedUserId?: string;
    selectedTechnologies?: string[];
    technologySkills?: { technology: string; proficiencyLevel: number }[];
  }) => {
    setIsSubmitting(true);
    try {
      // Extract technology data and clean up the employee data
      const { linkedUserId, selectedTechnologies, technologySkills, ...employeeData } = data;

      // Add userId if linking to existing user
      if (linkedUserId) {
        employeeData.userId = linkedUserId;
      }

      // Add technology skills if provided
      if (technologySkills && Array.isArray(technologySkills)) {
        employeeData.technologies = technologySkills.map((skill: any) => skill.technology);
      }

      // Create the employee
      const newEmployee = await createEmployeeMutation.mutateAsync(employeeData);

      // Handle technology assignments if provided
      if (selectedTechnologies && selectedTechnologies.length > 0 && technologySkills) {
        const employeeId = newEmployee._id || (newEmployee as any).id;
        try {
          // Add technologies to employee
          for (const skill of technologySkills) {
            await employeesApi.assignTechnology(employeeId, {
              technologyId: skill.technology,
              proficiencyLevel: skill.proficiencyLevel
            });
          }
          console.log('✅ Technologies assigned successfully');
        } catch (techError: any) {
          console.error('Failed to assign technologies:', techError);
          toast.error('Employee created but failed to assign some technologies');
        }
      }

      // Navigate to the new employee's detail page
      const employeeId = newEmployee._id || (newEmployee as any).id;
      if (employeeId) {
        router.push(`/admin/employees/${employeeId}`);
      } else {
        router.push('/admin/employees');
      }
    } catch (error: any) {
      // Error is handled by the mutation, but let's log it for debugging
      console.error('Create employee error in component:', {
        error,
        message: error?.message,
        response: error?.response,
        responseData: error?.response?.data,
        status: error?.response?.status
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/employees');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/employees">
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center space-x-2"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span>Back to Employees</span>
              </Button>
            </Link>
            
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-primary-500 to-accent-purple rounded-xl">
                <FiUsers className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Add New Employee</h1>
                <p className="text-primary-300 mt-1">Create a new employee record</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <DynamicEmployeeForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        </motion.div>


      </div>
    </div>
  );
}
