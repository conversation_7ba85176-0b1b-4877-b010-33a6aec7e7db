{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2024", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@Package/*": ["./src/package/*"], "@Modules/*": ["./src/modules/*"], "@Common/*": ["./src/common/*"]}}}