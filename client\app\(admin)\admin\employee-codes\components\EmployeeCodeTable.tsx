import { useEffect, useState } from 'react';
import { adminEmployeeCodesApi } from '@/lib/api/admin/employeeCodes';
import { toast } from 'react-hot-toast';
import { <PERSON>Key, FiCheck, FiClock, FiStar, FiUser, <PERSON>Users, FiShield } from 'react-icons/fi';
import { EmployeeCodeRole } from '@/lib/types/employee-code';

export default function EmployeeCodeTable() {
  const [codes, setCodes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [markingUsed, setMarkingUsed] = useState<string | null>(null);

  const fetchCodes = async () => {
    setLoading(true);
    try {
      const data = await adminEmployeeCodesApi.getUnused();
      setCodes(data.data);
    } catch (e: any) {
      toast.error(e.message || 'Failed to fetch codes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCodes();
  }, []);

  const markAsUsed = async (code: string) => {
    setMarkingUsed(code);
    try {
      await adminEmployeeCodesApi.markAsUsed(code);
      toast.success('Code marked as used successfully');
      fetchCodes();
    } catch (e: any) {
      toast.error(e.message || 'Failed to mark code as used');
    } finally {
      setMarkingUsed(null);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case EmployeeCodeRole.ADMIN:
        return <FiStar className="w-4 h-4 text-yellow-400" />;
      case EmployeeCodeRole.TEAM_LEADER:
        return <FiUsers className="w-4 h-4 text-blue-400" />;
      case EmployeeCodeRole.PROJECT_MANAGER:
        return <FiShield className="w-4 h-4 text-purple-400" />;
      default:
        return <FiUser className="w-4 h-4 text-slate-400" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case EmployeeCodeRole.ADMIN:
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case EmployeeCodeRole.TEAM_LEADER:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case EmployeeCodeRole.PROJECT_MANAGER:
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 border-2 border-purple-400/30 border-t-purple-400 rounded-full animate-spin"></div>
          <span className="text-slate-300">Loading employee codes...</span>
        </div>
      </div>
    );
  }

  if (codes?.length === 0) {
    return (
      <div className="text-center py-12 animate-fade-in">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-500/20 rounded-full mb-4">
          <FiKey className="w-8 h-8 text-purple-400" />
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">No Unused Codes</h3>
        <p className="text-slate-300">All employee codes have been used or none have been created yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-purple-500/20 rounded-lg">
          <FiClock className="w-5 h-5 text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Available Codes</h3>
          <p className="text-sm text-slate-300">{codes?.length} unused codes ready for assignment</p>
        </div>
      </div>

      {/* Table */}
      <div className="bg-slate-800/30 backdrop-blur-sm border border-slate-600/50 rounded-xl overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-700/50 border-b border-slate-600/50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-300 uppercase tracking-wider">
                  <div className="flex items-center gap-2">
                    <FiKey className="w-4 h-4 text-purple-400" />
                    Code
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-300 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-300 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-600/30">
              {codes?.map((code, index) => (
                <tr
                  key={code._id || index}
                  className="hover:bg-slate-700/30 transition-all duration-200 group"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors duration-200">
                        <FiKey className="w-4 h-4 text-purple-400" />
                      </div>
                      <span className="font-mono text-lg font-semibold text-white bg-slate-700/50 px-3 py-1 rounded-lg group-hover:bg-slate-700/70 transition-colors duration-200">
                        {code.code}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-slate-200 max-w-xs truncate" title={code.description}>
                      {code.description}
                    </p>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getRoleBadgeColor(code.role)}`}>
                      {getRoleIcon(code.role)}
                      <span>{code.role}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <button
                      onClick={() => markAsUsed(code.code)}
                      disabled={markingUsed === code.code}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 flex items-center gap-2"
                    >
                      {markingUsed === code.code ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          <span>Marking...</span>
                        </>
                      ) : (
                        <>
                          <FiCheck className="w-4 h-4" />
                          <span>Mark Used</span>
                        </>
                      )}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
