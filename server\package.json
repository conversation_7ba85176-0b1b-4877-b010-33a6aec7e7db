{"name": "silla-link", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build ", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch ", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch ", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "cd seeders && yarn start", "containers:up": "docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d", "containers:rebuild": "docker compose -f docker-compose.yml -f docker-compose.dev.yml up --build", "containers:down": "docker compose -f docker-compose.yml -f docker-compose.dev.yml down"}, "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.11", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.11", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.11", "@nestjs/serve-static": "^5.0.3", "@radix-ui/react-switch": "^1.2.5", "@swc/cli": "^0.7.7", "@swc/core": "^1.11.24", "@types/bcryptjs": "^3.0.0", "@types/sharp": "^0.32.0", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "express-rate-limit": "^7.5.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nest-winston": "^1.10.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pino-http": "^10.4.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sharp": "^0.33.5", "slugify": "^1.6.6", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@nestjs/testing": "^11.0.11", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}