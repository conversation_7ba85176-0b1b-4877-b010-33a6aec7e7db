import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { TaskService } from '../../services/task.service';
import { CreateTaskDto, UpdateTaskDto, AssignTaskDto, TaskFilterDto } from './../../dto/task.dto';
import { JwtAuthGuard } from '@Package/auth/guards';
import { RoleGuard } from '@Package/auth/guards/role.guard';
import { Roles } from '@Package/auth/decorators/roles.decorator';
import { UserRole } from '@Modules/user-management';

@Controller('admin/tasks')
@UseGuards(JwtAuthGuard, RoleGuard)
@Roles(UserRole.ADMIN)
export class TaskAdminController {
  constructor(private readonly taskService: TaskService) {}

  @Post()
  async create(@Body() createTaskDto: CreateTaskDto, @Request() req) {
    try {
      const task = await this.taskService.create(createTaskDto, req.user.id);
      return {
        success: true,
        message: 'Task created successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get()
  async findAll(@Query() filters: TaskFilterDto) {
    try {
      const tasks = await this.taskService.findAll(filters);
      return {
        success: true,
        message: 'Tasks retrieved successfully',
        data: tasks,
        count: tasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('statistics')
  async getStatistics(@Query('projectId') projectId?: string) {
    try {
      const stats = await this.taskService.getTaskStatistics(projectId);
      return {
        success: true,
        message: 'Task statistics retrieved successfully',
        data: stats,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('project/:projectId')
  async findByProject(@Param('projectId') projectId: string) {
    try {
      const tasks = await this.taskService.findByProject(projectId);
      return {
        success: true,
        message: 'Project tasks retrieved successfully',
        data: tasks,
        count: tasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('assignee/:assigneeId')
  async findByAssignee(@Param('assigneeId') assigneeId: string) {
    try {
      const tasks = await this.taskService.findByAssignee(assigneeId);
      return {
        success: true,
        message: 'Assigned tasks retrieved successfully',
        data: tasks,
        count: tasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      const task = await this.taskService.findById(id);
      return {
        success: true,
        message: 'Task retrieved successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.update(id, updateTaskDto, req.user.id);
      return {
        success: true,
        message: 'Task updated successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id/assign')
  async assign(
    @Param('id') id: string,
    @Body() assignTaskDto: AssignTaskDto,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.assign(id, assignTaskDto, req.user.id);
      return {
        success: true,
        message: 'Task assigned successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: string,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.updateStatus(id, status, req.user.id);
      return {
        success: true,
        message: 'Task status updated successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Post(':id/attachments')
  @UseInterceptors(FileInterceptor('file'))
  async addAttachment(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
  ) {
    try {
      if (!file) {
        throw new BadRequestException('No file uploaded');
      }

      const task = await this.taskService.addAttachment(
        id,
        file.filename,
        file.originalname,
        file.size,
        req.user.id,
      );

      return {
        success: true,
        message: 'Attachment added successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id/attachments/:attachmentId')
  async removeAttachment(
    @Param('id') id: string,
    @Param('attachmentId') attachmentId: string,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.removeAttachment(id, attachmentId, req.user.id);
      return {
        success: true,
        message: 'Attachment removed successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await this.taskService.delete(id, req.user.id);
      return {
        success: true,
        message: 'Task deleted successfully',
      };
    } catch (error) {
      throw error;
    }
  }
}
