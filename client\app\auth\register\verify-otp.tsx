'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import axios from 'axios';
import { Card, CardHeader } from '@/components/shared/Card';
import { FormField } from '@/components/shared/forms/FormField';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { Steps } from '@/components/shared/navigation/Breadcrumb';

const otpSchema = z.object({
  otp: z.string().length(6, 'OTP must be exactly 6 digits').regex(/^\d+$/, 'OTP must contain only numbers'),
});

type OtpFormData = z.infer<typeof otpSchema>;

export default function VerifyOtpPage() {
  const params = useSearchParams();
  const email = params.get('email') || '';
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const router = useRouter();
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<OtpFormData>({
    resolver: zodResolver(otpSchema),
  });

  const otpValue = watch('otp') || '';

  // Countdown timer for resend
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [resendCooldown]);

  // Auto-focus and handle OTP input
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const pastedValue = value.slice(0, 6);
      setValue('otp', pastedValue);
      const nextIndex = Math.min(index + pastedValue.length, 5);
      inputRefs.current[nextIndex]?.focus();
    } else {
      // Handle single character
      const newOtp = otpValue.split('');
      newOtp[index] = value;
      setValue('otp', newOtp.join(''));

      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpValue[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const onSubmit = async (data: OtpFormData) => {
    setLoading(true);
    setError('');

    try {
      await axios.post('/api/auth/verify-employee-code-otp', { email, otp: data.otp });
      router.push(`/auth/register/set-password?email=${encodeURIComponent(email)}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'OTP verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setResendLoading(true);
    setError('');

    try {
      const registrationData = sessionStorage.getItem('registrationData');
      if (!registrationData) {
        setError('Registration data not found. Please start registration again.');
        return;
      }

      await axios.post('/api/auth/initiate-registration', JSON.parse(registrationData));
      setResendCooldown(60); // 60 seconds cooldown
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to resend OTP. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const steps = [
    { title: 'Employee Details', description: 'Enter your information and employee code' },
    { title: 'Verify Email', description: 'Confirm your email with OTP' },
    { title: 'Set Password', description: 'Create your secure password' },
    { title: 'Complete', description: 'Registration successful' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <Steps
            current={1}
            items={steps}
            size="sm"
            direction="horizontal"
          />
        </div>

        <Card className="p-8">
          <CardHeader
            title="Verify Your Email"
            subtitle="Step 2 of 4: Enter the verification code"
            className="text-center mb-6"
          />

          <div className="text-center mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              We've sent a 6-digit verification code to
            </p>
            <p className="font-medium text-gray-900 dark:text-white mt-1">
              {email}
            </p>
          </div>

          {error && (
            <Alert
              type="error"
              message={error}
              closable
              onClose={() => setError('')}
              className="mb-6"
            />
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              label="Verification Code"
              required
              error={errors.otp?.message}
              description="Enter the 6-digit code sent to your email"
            >
              <div className="flex justify-center space-x-2">
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <input
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    type="text"
                    maxLength={6}
                    className="w-12 h-12 text-center text-lg font-semibold border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={otpValue[index] || ''}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    disabled={loading}
                  />
                ))}
              </div>
            </FormField>

            <Button
              type="submit"
              fullWidth
              loading={loading}
              loadingText="Verifying code..."
              size="lg"
              disabled={otpValue.length !== 6}
            >
              Verify & Continue
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Didn't receive the code?{' '}
              {resendCooldown > 0 ? (
                <span className="text-gray-500">
                  Resend in {resendCooldown}s
                </span>
              ) : (
                <button
                  onClick={handleResendOtp}
                  disabled={resendLoading}
                  className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50"
                >
                  {resendLoading ? 'Sending...' : 'Resend code'}
                </button>
              )}
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}
