'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/hooks/use-auth';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiMenu,
  FiX,
  FiBell,
  FiSearch,
  FiUser,
  FiLogOut,
  FiSettings,
  FiChevronLeft,
  FiChevronRight
} from 'react-icons/fi';
import { AdminNavigation } from './AdminNavigation';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { Spinner } from '@/components/shared/Spinner';
import { cn } from '@/lib/utils/cn';

interface AdminLayoutWrapperProps {
  children: React.ReactNode;
}

export const AdminLayoutWrapper: React.FC<AdminLayoutWrapperProps> = ({ children }) => {
  const { user, isAuthenticated, isAdmin, isLoading, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      if (mobile) {
        setSidebarCollapsed(false);
        setSidebarOpen(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close mobile sidebar on route change
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Authentication and authorization checks
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/auth/login');
      } else if (!isAdmin) {
        router.push('/');
      }
    }
  }, [isAuthenticated, isAdmin, isLoading, router]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-primary-300">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // Unauthorized access
  if (!isAuthenticated || !isAdmin) {
    return (
      <div className="flex h-screen items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
        <Alert
          type="error"
          message="Access denied. Admin privileges required."
        />
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
      {/* Mobile sidebar backdrop */}
      <AnimatePresence>
        {sidebarOpen && isMobile && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          width: isMobile
            ? (sidebarOpen ? '280px' : '0px')
            : (sidebarCollapsed ? '80px' : '280px'),
          x: isMobile && !sidebarOpen ? '-100%' : '0%'
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={cn(
          'fixed lg:relative z-50 h-full bg-dark-800/95 backdrop-blur-xl border-r border-dark-700 shadow-2xl',
          'flex flex-col overflow-hidden'
        )}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-dark-700">
          {!sidebarCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-purple rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SL</span>
              </div>
              <div>
                <h1 className="text-lg font-bold text-white">SillaLink</h1>
                <p className="text-xs text-primary-300">Admin Panel</p>
              </div>
            </div>
          )}
          
          {/* Sidebar controls */}
          <div className="flex items-center space-x-2">
            {!isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                className="p-2"
              >
                {sidebarCollapsed ? (
                  <FiChevronRight className="w-4 h-4" />
                ) : (
                  <FiChevronLeft className="w-4 h-4" />
                )}
              </Button>
            )}
            
            {isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="p-2"
              >
                <FiX className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        {/* User Profile */}
        {!sidebarCollapsed && (
          <div className="p-4 border-b border-dark-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500/20 to-accent-purple/20 rounded-full flex items-center justify-center border border-primary-500/30">
                <FiUser className="w-5 h-5 text-primary-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.name || 'Admin User'}
                </p>
                <p className="text-xs text-primary-300 truncate">
                  {user?.role || 'Administrator'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto p-4">
          <AdminNavigation
            userRole={user?.role || 'USER'}
            isCollapsed={sidebarCollapsed}
          />
        </div>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-dark-700">
          {sidebarCollapsed ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="w-full p-2 text-primary-300 hover:text-white hover:bg-dark-700"
            >
              <FiLogOut className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              variant="secondary"
              size="sm"
              onClick={handleLogout}
              className="w-full"
            >
              <FiLogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          )}
        </div>
      </motion.aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header */}
        <header className="bg-dark-800/95 backdrop-blur-xl border-b border-dark-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Mobile menu button */}
              {isMobile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(true)}
                  className="p-2 text-primary-300 hover:text-white hover:bg-dark-700"
                >
                  <FiMenu className="w-5 h-5" />
                </Button>
              )}

              {/* Page title */}
              <div>
                <h2 className="text-xl font-semibold text-white">
                  {pathname === '/admin/dashboard' ? 'Dashboard' :
                   pathname.split('/').pop()?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </h2>
              </div>
            </div>

            {/* Header actions */}
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="p-2 text-primary-300 hover:text-white hover:bg-dark-700">
                <FiSearch className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="sm" className="p-2 relative text-primary-300 hover:text-white hover:bg-dark-700">
                <FiBell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>
              <Button variant="ghost" size="sm" className="p-2 text-primary-300 hover:text-white hover:bg-dark-700">
                <FiSettings className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayoutWrapper;
