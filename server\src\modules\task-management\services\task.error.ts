import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { MongoError } from 'mongodb';

@Injectable()
export class TaskError {
  private readonly logger = new Logger(TaskError.name);

  handleError(error: any, context: string): HttpException {
    this.logger.error(`${context}: ${error.message}`, error.stack);

    // Handle known HTTP exceptions
    if (error instanceof HttpException) {
      return error;
    }

    // Handle MongoDB errors
    if (error instanceof MongoError) {
      return this.handleMongoError(error, context);
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return this.handleValidationError(error, context);
    }

    // Handle cast errors (invalid ObjectId)
    if (error.name === 'CastError') {
      return new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Invalid ID format',
          error: 'Bad Request',
          context,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    // Default error
    return new HttpException(
      {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'An unexpected error occurred',
        error: 'Internal Server Error',
        context,
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  private handleMongoError(error: MongoError, context: string): HttpException {
    switch (error.code) {
      case 11000: // Duplicate key error
        return new HttpException(
          {
            statusCode: HttpStatus.CONFLICT,
            message: 'Task with this identifier already exists',
            error: 'Conflict',
            context,
          },
          HttpStatus.CONFLICT,
        );
      default:
        return new HttpException(
          {
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: 'Database operation failed',
            error: 'Internal Server Error',
            context,
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
  }

  private handleValidationError(error: any, context: string): HttpException {
    const messages = Object.values(error.errors).map((err: any) => err.message);
    
    return new HttpException(
      {
        statusCode: HttpStatus.BAD_REQUEST,
        message: messages,
        error: 'Validation Error',
        context,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}
