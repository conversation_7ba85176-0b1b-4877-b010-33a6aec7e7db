@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {

  --font-poppins: Poppins, sans-serif;
  --font-inspiration: inspiration, cursive;

  --color-background: var(--foreground);
  --color-foreground: var(--foreground);
  --color-primary: #6B2C91;
  --color-secondary: #E3D8E9;
  --color-purple: #CBACF9;
  --color-gray-100: #D1D1D1;
  --color-indego-dark: #0f0f23;
  --color-indego-darker: #0a0a1a;
  --color-dashboard: #F5F5F6;

  /* Enhanced Design System Colors */
  --color-primary-50: #f3f1ff;
  --color-primary-100: #ebe5ff;
  --color-primary-200: #d9ceff;
  --color-primary-300: #bea6ff;
  --color-primary-400: #9f75ff;
  --color-primary-500: #843dff;
  --color-primary-600: #7916ff;
  --color-primary-700: #6b04fd;
  --color-primary-800: #5a03d4;
  --color-primary-900: #4b05ad;
  --color-primary-950: #2c0076;

  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #475569;
  --color-dark-700: #334155;
  --color-dark-800: #1e293b;
  --color-dark-900: #0f172a;
  --color-dark-950: #020617;

  --color-accent-purple: #8b5cf6;
  --color-accent-blue: #3b82f6;
  --color-accent-cyan: #06b6d4;
  --color-accent-emerald: #10b981;
  --color-accent-amber: #f59e0b;
  --color-accent-rose: #f43f5e;

  --animate-spotlight: spotlight 2s ease 0.75s 1 forwards;
  --animate-scroll: scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite;

  /* Enhanced Shadows */
  --shadow-glow: 0 0 20px rgba(121, 22, 255, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(121, 22, 255, 0.2);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  --shadow-glass: 0 5px 50px inset rgba(255, 255, 255, 0.05), 0 6px 50px inset rgba(255, 255, 255, 0.05), 0 30px 50px inset rgba(255, 255, 255, 0.1),60px 70px 1500px inset rgba(255, 255, 255, 0.1);
  --shadow-white: 0 5px 20px inset rgba(255, 255, 255, 0.05), 0 6px 3px inset rgba(255, 255, 255, 0.05), 0 30px 20px inset rgba(255, 255, 255, 0.1);
  --shadow-icon: 0.4px 0.4px 0px #CBACF9,-0.4px -0.4px 0px #CBACF9;
  --shadow-chart-icon: 0 -2px 1px inset rgba(0, 0, 0 , 0.2),-1px 2px 0px inset rgba(255, 255, 255,0.2);
  --shadow-chart-card: 24px -2px 19px rgb(0, 0, 0),59px 19px 60px rgb(0, 0, 0);

  --text-verySmall:10px;
  --text-extraSmall:12px;
  --text-small:14px;
  --text-regular:18px;
  --text-medium:24px;
  --text-large:36px;
  --text-extraLarge:42px;

  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-primary-foreground: var(--primary-foreground);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

.hiddenScrollbar {
  scrollbar-width: none;
}

@keyframes spotlight {
  0% {
    opacity: 0;
    transform: translate(-72%, -62%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -40%) scale(1);
  }
}

@keyframes scroll {
  to {
    transform: translate(calc(-50% - 0.5rem));
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

:root {

  --radius: 0.625rem;

  --background: oklch(1 0 0);

  --foreground: oklch(0.145 0 0);

  --card: oklch(1 0 0);

  --card-foreground: oklch(0.145 0 0);

  --popover: oklch(1 0 0);

  --popover-foreground: oklch(0.145 0 0);

  --primary: oklch(0.205 0 0);

  --primary-foreground: oklch(0.985 0 0);

  --secondary: oklch(0.97 0 0);

  --secondary-foreground: oklch(0.205 0 0);

  --muted: oklch(0.97 0 0);

  --muted-foreground: oklch(0.556 0 0);

  --accent: oklch(0.97 0 0);

  --accent-foreground: oklch(0.205 0 0);

  --destructive: oklch(0.577 0.245 27.325);

  --border: oklch(0.922 0 0);

  --input: oklch(0.922 0 0);

  --ring: oklch(0.708 0 0);

  --chart-1: oklch(0.646 0.222 41.116);

  --chart-2: oklch(0.6 0.118 184.704);

  --chart-3: oklch(0.398 0.07 227.392);

  --chart-4: oklch(0.828 0.189 84.429);

  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);

  --sidebar-foreground: oklch(0.145 0 0);

  --sidebar-primary: oklch(0.205 0 0);

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.97 0 0);

  --sidebar-accent-foreground: oklch(0.205 0 0);

  --sidebar-border: oklch(0.922 0 0);

  --sidebar-ring: oklch(0.708 0 0);
}

.dark {

  --background: oklch(0.145 0 0);

  --foreground: oklch(0.985 0 0);

  --card: oklch(0.205 0 0);

  --card-foreground: oklch(0.985 0 0);

  --popover: oklch(0.205 0 0);

  --popover-foreground: oklch(0.985 0 0);

  --primary: oklch(0.922 0 0);

  --primary-foreground: oklch(0.205 0 0);

  --secondary: oklch(0.269 0 0);

  --secondary-foreground: oklch(0.985 0 0);

  --muted: oklch(0.269 0 0);

  --muted-foreground: oklch(0.708 0 0);

  --accent: oklch(0.269 0 0);

  --accent-foreground: oklch(0.985 0 0);

  --destructive: oklch(0.704 0.191 22.216);

  --border: oklch(1 0 0 / 10%);

  --input: oklch(1 0 0 / 15%);

  --ring: oklch(0.556 0 0);

  --chart-1: oklch(0.488 0.243 264.376);

  --chart-2: oklch(0.696 0.17 162.48);

  --chart-3: oklch(0.769 0.188 70.08);

  --chart-4: oklch(0.627 0.265 303.9);

  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.205 0 0);

  --sidebar-foreground: oklch(0.985 0 0);

  --sidebar-primary: oklch(0.488 0.243 264.376);

  --sidebar-primary-foreground: oklch(0.985 0 0);

  --sidebar-accent: oklch(0.269 0 0);

  --sidebar-accent-foreground: oklch(0.985 0 0);

  --sidebar-border: oklch(1 0 0 / 10%);

  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Enhanced Design System Utilities */
  .bg-primary-50 { background-color: var(--color-primary-50); }
  .bg-primary-100 { background-color: var(--color-primary-100); }
  .bg-primary-200 { background-color: var(--color-primary-200); }
  .bg-primary-300 { background-color: var(--color-primary-300); }
  .bg-primary-400 { background-color: var(--color-primary-400); }
  .bg-primary-500 { background-color: var(--color-primary-500); }
  .bg-primary-600 { background-color: var(--color-primary-600); }
  .bg-primary-700 { background-color: var(--color-primary-700); }
  .bg-primary-800 { background-color: var(--color-primary-800); }
  .bg-primary-900 { background-color: var(--color-primary-900); }
  .bg-primary-950 { background-color: var(--color-primary-950); }

  .text-primary-50 { color: var(--color-primary-50); }
  .text-primary-100 { color: var(--color-primary-100); }
  .text-primary-200 { color: var(--color-primary-200); }
  .text-primary-300 { color: var(--color-primary-300); }
  .text-primary-400 { color: var(--color-primary-400); }
  .text-primary-500 { color: var(--color-primary-500); }
  .text-primary-600 { color: var(--color-primary-600); }
  .text-primary-700 { color: var(--color-primary-700); }
  .text-primary-800 { color: var(--color-primary-800); }
  .text-primary-900 { color: var(--color-primary-900); }
  .text-primary-950 { color: var(--color-primary-950); }

  .border-primary-50 { border-color: var(--color-primary-50); }
  .border-primary-100 { border-color: var(--color-primary-100); }
  .border-primary-200 { border-color: var(--color-primary-200); }
  .border-primary-300 { border-color: var(--color-primary-300); }
  .border-primary-400 { border-color: var(--color-primary-400); }
  .border-primary-500 { border-color: var(--color-primary-500); }
  .border-primary-600 { border-color: var(--color-primary-600); }
  .border-primary-700 { border-color: var(--color-primary-700); }
  .border-primary-800 { border-color: var(--color-primary-800); }
  .border-primary-900 { border-color: var(--color-primary-900); }
  .border-primary-950 { border-color: var(--color-primary-950); }

  .bg-dark-50 { background-color: var(--color-dark-50); }
  .bg-dark-100 { background-color: var(--color-dark-100); }
  .bg-dark-200 { background-color: var(--color-dark-200); }
  .bg-dark-300 { background-color: var(--color-dark-300); }
  .bg-dark-400 { background-color: var(--color-dark-400); }
  .bg-dark-500 { background-color: var(--color-dark-500); }
  .bg-dark-600 { background-color: var(--color-dark-600); }
  .bg-dark-700 { background-color: var(--color-dark-700); }
  .bg-dark-800 { background-color: var(--color-dark-800); }
  .bg-dark-900 { background-color: var(--color-dark-900); }
  .bg-dark-950 { background-color: var(--color-dark-950); }

  .text-dark-50 { color: var(--color-dark-50); }
  .text-dark-100 { color: var(--color-dark-100); }
  .text-dark-200 { color: var(--color-dark-200); }
  .text-dark-300 { color: var(--color-dark-300); }
  .text-dark-400 { color: var(--color-dark-400); }
  .text-dark-500 { color: var(--color-dark-500); }
  .text-dark-600 { color: var(--color-dark-600); }
  .text-dark-700 { color: var(--color-dark-700); }
  .text-dark-800 { color: var(--color-dark-800); }
  .text-dark-900 { color: var(--color-dark-900); }
  .text-dark-950 { color: var(--color-dark-950); }

  .bg-accent-purple { background-color: var(--color-accent-purple); }
  .bg-accent-blue { background-color: var(--color-accent-blue); }
  .bg-accent-cyan { background-color: var(--color-accent-cyan); }
  .bg-accent-emerald { background-color: var(--color-accent-emerald); }
  .bg-accent-amber { background-color: var(--color-accent-amber); }
  .bg-accent-rose { background-color: var(--color-accent-rose); }

  .text-accent-purple { color: var(--color-accent-purple); }
  .text-accent-blue { color: var(--color-accent-blue); }
  .text-accent-cyan { color: var(--color-accent-cyan); }
  .text-accent-emerald { color: var(--color-accent-emerald); }
  .text-accent-amber { color: var(--color-accent-amber); }
  .text-accent-rose { color: var(--color-accent-rose); }

  .shadow-glow { box-shadow: var(--shadow-glow); }
  .shadow-glow-lg { box-shadow: var(--shadow-glow-lg); }
  .shadow-glass { box-shadow: var(--shadow-glass); }

  .bg-indego-dark { background-color: var(--color-indego-dark); }
  .bg-indego-darker { background-color: var(--color-indego-darker); }
}

@layer utilities {
  /* Touch-friendly interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Line clamping for text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Custom breakpoint for extra small screens */
  @media (min-width: 475px) {
    .xs\:flex {
      display: flex;
    }

    .xs\:hidden {
      display: none;
    }

    .xs\:block {
      display: block;
    }
  }

  /* Responsive grid improvements */
  .grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .grid-responsive {
      gap: 1.5rem;
    }
  }

  /* Safe area padding for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}