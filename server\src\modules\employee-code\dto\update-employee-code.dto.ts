import { IsString, Length, IsEnum, IsOptional, IsNotEmpty, IsBoolean } from 'class-validator';
import { EmployeeCodeRole } from '../employee-code.schema';

export class UpdateEmployeeCodeDto {
  @IsOptional()
  @IsString()
  @Length(4, 10)
  code?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  description?: string;

  @IsOptional()
  @IsEnum(EmployeeCodeRole)
  role?: EmployeeCodeRole;

  @IsOptional()
  @IsBoolean()
  isUsed?: boolean;
}
