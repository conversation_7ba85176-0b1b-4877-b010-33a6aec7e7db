# Employee Management System

## Overview
This module manages detailed employee information after successful signup. It supports employee profile creation, update, deletion, and linking with technologies and projects.

## Features
- Upon successful authentication, a new employee record is created.
- Admin can:
  - View all employees.
  - Edit any employee’s profile.
  - Add additional info (address, emergency contact, etc.).
  - Delete employees.
- Employees can be linked to:
  - Technologies (from the Technology System).
  - Projects (from the Project Management System).

## Fields
- User ID (ref to authentication user)
- First Name / Last Name
- Email / Phone
- Country, City, Street
- Emergency Contact
- Hiring Date
- Personal links (LinkedIn, GitHub, etc.)
- Profile Image
- Technologies (array of refs to tech documents)
- Role (inferred from employee code)

## Integration with Other Systems
- **Authentication System**: Triggers profile creation.
- **Technology System**: Employees are linked to technologies.
- **Project Management System**: Employees are assigned to projects and tasks.
- **Employee Code System**: Code is marked used and deleted upon employee deletion.

## Admin Interface
- Full CRUD operations.
- Optional search/filter (by name, role, tech).
- Option to reset employee's password (linked to auth module).

## Notes
- Employee deletion removes the link to their employee code.
- Strongly validated input fields.

---

**Next**: `TECHNOLOGY.md`

