import apiClient from '../client';
import { EmployeeC<PERSON>, CreateEmployeeCodeDto, UpdateEmployeeCodeDto, EmployeeCodeFilters, BulkGenerateCodesDto, BulkUpdateCodesDto, BulkDeleteCodesDto } from '@/lib/types/employee-code';
import { ApiResponse, PaginatedResponse } from '@/lib/types/api';

export const adminEmployeeCodesApi = {
  // Get all employee codes with filters and pagination
  getAll: (params: EmployeeCodeFilters): Promise<ApiResponse<PaginatedResponse<EmployeeCode>>> =>
    apiClient.get('/admin/employee-codes', { params }),

  // Get employee code by ID
  getById: (id: string): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.get(`/admin/employee-codes/${id}`),

  // Create new employee code
  create: (data: CreateEmployeeCodeDto): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.post('/admin/employee-codes', data),

  // Update employee code
  update: (id: string, data: UpdateEmployeeCodeDto): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.patch(`/admin/employee-codes/${id}`, data),

  // Delete employee code
  delete: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/employee-codes/${id}`),

  // Get all unused codes
  getUnused: (): Promise<ApiResponse<EmployeeCode[]>> =>
    apiClient.get('/admin/employee-codes/unused'),

  // Check code validity
  check: (code: string): Promise<ApiResponse<{ isValid: boolean; employeeCode?: EmployeeCode }>> =>
    apiClient.get(`/admin/employee-codes/check/${code}`),

  // Mark code as used
  markAsUsed: (code: string): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.patch(`/admin/employee-codes/use/${code}`),

  // Activate employee code
  activate: (id: string): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.patch(`/admin/employee-codes/${id}/activate`),

  // Deactivate employee code
  deactivate: (id: string): Promise<ApiResponse<EmployeeCode>> =>
    apiClient.patch(`/admin/employee-codes/${id}/deactivate`),

  // Get employee code statistics
  getStats: (): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/employee-codes/stats'),

  // Bulk operations
  bulkGenerate: (data: BulkGenerateCodesDto): Promise<ApiResponse<EmployeeCode[]>> =>
    apiClient.post('/admin/employee-codes/bulk-generate', data),

  bulkUpdate: (data: BulkUpdateCodesDto): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/employee-codes/bulk-update', data),

  bulkDelete: (ids: string[]): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/employee-codes/bulk-delete', { ids }),

  // Export employee codes
  export: (format: 'csv' | 'excel', filters?: EmployeeCodeFilters): Promise<Blob> =>
    apiClient.get('/admin/employee-codes/export', {
      params: { format, ...filters },
      responseType: 'blob'
    }),
};
