'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import {
  FiUser,
  FiBriefcase,
  FiSearch,
  FiPlus,
  FiX,
  FiCheck,
  FiAlertCircle,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCode,
  FiUserPlus,
  FiImage
} from 'react-icons/fi';
import {
  CreateEmployeeData,
  EmployeeDepartment,
  EmployeeStatus,
  EmploymentType,
  DEPARTMENT_OPTIONS,
  STATUS_OPTIONS,
  EMPLOYMENT_TYPE_OPTIONS,
  GENDER_OPTIONS,
  MARITAL_STATUS_OPTIONS
} from '@/lib/types/employee';
import { User } from '@/lib/types/auth';
import { Technology, TechnologyStatus } from '@/lib/types/technology';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { Modal } from '@/components/shared/Modal';
import { ImageUpload } from '@/components/ui/image-upload';
import { useUsers } from '@/lib/hooks/use-users';
import { useTechnologies } from '@/lib/hooks/use-technologies';
import { useCreateUser } from '@/lib/hooks/use-users';
import { HandleError } from '@/lib/utils/error-handler';
import {
  UserSelectionStep,
  EmployeeDetailsStep,
  TechnologiesStep,
  ReviewStep
} from './DynamicEmployeeFormSteps';

interface DynamicEmployeeFormProps {
  onSubmit: (data: CreateEmployeeData & {
    linkedUserId?: string;
    selectedTechnologies?: string[];
    technologySkills?: { technology: string; proficiencyLevel: number }[];
  }) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

interface UserSearchResult extends User {
  isExistingEmployee?: boolean;
}

interface TechnologySelection {
  technologyId: string;
  technology: Technology;
  proficiencyLevel: number;
}

export const DynamicEmployeeForm: React.FC<DynamicEmployeeFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  // Form state
  const [currentStep, setCurrentStep] = useState<'user' | 'details' | 'technologies' | 'image' | 'review'>('user');
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
  const [createNewUser, setCreateNewUser] = useState(false);
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [selectedTechnologies, setSelectedTechnologies] = useState<TechnologySelection[]>([]);
  const [technologySearchQuery, setTechnologySearchQuery] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // API hooks
  const { data: usersData, isLoading: usersLoading } = useUsers({
    search: userSearchQuery,
    limit: 10
  });
  const { data: technologiesData, isLoading: technologiesLoading } = useTechnologies({
    search: technologySearchQuery,
    limit: 20,
    status: TechnologyStatus.ACTIVE
  });
  const createUserMutation = useCreateUser();

  // Form setup
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<CreateEmployeeData>({
    defaultValues: {
      // Basic Information
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      birthDate: '',
      nationalId: '',
      passportNumber: '',

      // Contact Information
      address: {
        country: '',
        city: '',
        street: '',
        postalCode: ''
      },
      city: '',
      country: '',
      emergencyContact: '',
      emergencyPhone: '',

      // Professional Information
      position: '',
      jobTitle: '',
      department: EmployeeDepartment.OTHER,
      employmentType: EmploymentType.FULL_TIME,
      status: EmployeeStatus.ACTIVE,
      hireDate: new Date().toISOString().split('T')[0],
      startDate: new Date().toISOString().split('T')[0],
      managerId: '',

      // Salary and Contract
      salaryDetails: {
        amount: 0,
        currency: 'USD'
      },
      salary: 0,
      contractStartDate: '',
      contractEndDate: '',

      // Media & Assets
      profileImage: '',
      avatar: '',

      // Additional Information
      bio: '',
      skills: [],
      certifications: [],
      linkedinProfile: '',
      githubProfile: '',
      portfolioUrl: '',
      notes: '',
      isFeatured: false,
      performanceRating: 0
    } as CreateEmployeeData
  });

  const watchedData = watch();

  // Filter users based on search - fix data structure access
  const filteredUsers = usersData?.data?.data ?
    (Array.isArray(usersData.data.data) ? usersData.data.data : []).filter((user: User) =>
      user.firstName.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.lastName.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchQuery.toLowerCase())
    ) : [];

  // Filter technologies based on search - show all matching technologies
  const filteredTechnologies = technologiesData?.technologies?.filter((tech: Technology) =>
    tech.name.toLowerCase().includes(technologySearchQuery.toLowerCase())
  ) || [];

  // Show all filtered technologies in search (don't filter out selected ones)
  const availableTechnologies = filteredTechnologies;

  // Handle user selection
  const handleUserSelect = (user: UserSearchResult) => {
    setSelectedUser(user);
    setValue('firstName', user.firstName);
    setValue('lastName', user.lastName);
    setValue('email', user.email);
    if (user.phone) setValue('phone', user.phone);
    setUserSearchQuery('');
    setCreateNewUser(false);
  };

  // Handle new user creation
  const handleCreateNewUser = () => {
    setCreateNewUser(true);
    setSelectedUser(null);
    reset({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      position: '',
      department: EmployeeDepartment.OTHER,
      employmentType: EmploymentType.FULL_TIME,
      status: EmployeeStatus.ACTIVE,
      hireDate: new Date().toISOString().split('T')[0]
    } as CreateEmployeeData);
  };

  // Handle technology selection
  const handleTechnologySelect = (technology: Technology) => {
    const newSelection: TechnologySelection = {
      technologyId: technology._id,
      technology,
      proficiencyLevel: 50 // Default proficiency
    };
    setSelectedTechnologies(prev => [...prev, newSelection]);
    setTechnologySearchQuery('');
  };

  // Handle technology removal
  const handleTechnologyRemove = (technologyId: string) => {
    setSelectedTechnologies(prev => prev.filter(tech => tech.technologyId !== technologyId));
  };

  // Handle proficiency level change
  const handleProficiencyChange = (technologyId: string, level: number) => {
    setSelectedTechnologies(prev =>
      prev.map(tech =>
        tech.technologyId === technologyId
          ? { ...tech, proficiencyLevel: level }
          : tech
      )
    );
  };

  // Handle image upload
  const handleImageChange = (file: File | null, preview?: string) => {
    setImageFile(file);
    setImagePreview(preview || null);
  };

  // Handle form submission
  const handleFormSubmit = (data: CreateEmployeeData) => {
    setShowConfirmModal(true);
  };

  const handleConfirmSubmit = async () => {
    const formData = watchedData;
    
    try {
      let linkedUserId = selectedUser?._id;

      // Create new user if needed
      if (createNewUser && !selectedUser) {
        // Use password from form or generate a secure temporary password
        let userPassword = formData.password;

        if (!userPassword) {
          // Generate a secure temporary password if none provided
          const generateTempPassword = () => {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
            let password = '';
            for (let i = 0; i < 12; i++) {
              password += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return password;
          };
          userPassword = generateTempPassword();
        }

        const newUser = await createUserMutation.mutateAsync({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone || '',
          role: 'employee',
          password: userPassword,
          isActive: true
        });
        linkedUserId = newUser.data._id;

        // Show password to admin if it was generated
        if (!formData.password) {
          toast.success(`User created successfully! Generated password: ${userPassword}`, {
            duration: 10000,
          });
        } else {
          toast.success('User created successfully with provided password!');
        }
      }

      // Prepare submission data with proper data transformation
      const { password, ...employeeData } = formData;

      const submissionData = {
        ...employeeData,
        // Remove maritalStatus if it's empty string to avoid validation error
        ...(employeeData.maritalStatus ? { maritalStatus: employeeData.maritalStatus } : {}),
        ...(linkedUserId && { linkedUserId }),
        // Include image file if uploaded
        ...(imageFile && { imageFile }),
        // Ensure technologies are valid MongoDB IDs
        technologies: selectedTechnologies.map(tech => tech.technologyId),
        selectedTechnologies: selectedTechnologies.map(tech => tech.technologyId),
        technologySkills: selectedTechnologies.map(tech => ({
          technology: tech.technologyId,
          proficiencyLevel: tech.proficiencyLevel
        }))
      };

      onSubmit(submissionData);
      setShowConfirmModal(false);
    } catch (error) {
      console.error('Error creating employee:', error);
      const errorMessage = HandleError(error);
      toast.error(errorMessage);
    }
  };

  // Step navigation
  const canProceedToDetails = selectedUser || createNewUser;
  const canProceedToTechnologies = canProceedToDetails && watchedData.firstName && watchedData.lastName && watchedData.email && watchedData.position;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Step Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[
            { key: 'user', title: 'User Selection', icon: FiUser },
            { key: 'details', title: 'Employee Details', icon: FiBriefcase },
            { key: 'technologies', title: 'Technologies', icon: FiCode },
            { key: 'image', title: 'Profile Image', icon: FiImage },
            { key: 'review', title: 'Review', icon: FiCheck }
          ].map((step, index) => {
            const isActive = currentStep === step.key;
            const isCompleted =
              (step.key === 'user' && canProceedToDetails) ||
              (step.key === 'details' && canProceedToTechnologies) ||
              (step.key === 'technologies' && (currentStep === 'image' || currentStep === 'review')) ||
              (step.key === 'image' && currentStep === 'review');

            return (
              <div key={step.key} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 ${
                  isCompleted 
                    ? 'bg-green-500 border-green-500 text-white' 
                    : isActive 
                      ? 'bg-primary-500 border-primary-500 text-white' 
                      : 'border-dark-600 text-primary-400'
                }`}>
                  {isCompleted ? (
                    <FiCheck className="w-5 h-5" />
                  ) : (
                    <step.icon className="w-5 h-5" />
                  )}
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  isActive ? 'text-white' : 'text-primary-400'
                }`}>
                  {step.title}
                </span>
                {index < 3 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-dark-600'
                  }`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {currentStep === 'user' && (
              <UserSelectionStep
                userSearchQuery={userSearchQuery}
                setUserSearchQuery={setUserSearchQuery}
                filteredUsers={filteredUsers}
                selectedUser={selectedUser}
                createNewUser={createNewUser}
                onUserSelect={handleUserSelect}
                onCreateNewUser={handleCreateNewUser}
                usersLoading={usersLoading}
              />
            )}

            {currentStep === 'details' && (
              <EmployeeDetailsStep
                register={register}
                errors={errors}
                control={control}
                selectedUser={selectedUser}
                createNewUser={createNewUser}
              />
            )}

            {currentStep === 'technologies' && (
              <TechnologiesStep
                technologySearchQuery={technologySearchQuery}
                setTechnologySearchQuery={setTechnologySearchQuery}
                filteredTechnologies={availableTechnologies}
                selectedTechnologies={selectedTechnologies}
                onTechnologySelect={handleTechnologySelect}
                onTechnologyRemove={handleTechnologyRemove}
                onProficiencyChange={handleProficiencyChange}
                technologiesLoading={technologiesLoading}
              />
            )}

            {currentStep === 'image' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-2">Profile Image</h3>
                  <p className="text-primary-300">Upload a profile image for the employee (optional)</p>
                </div>

                <div className="max-w-md mx-auto">
                  <ImageUpload
                    value={imagePreview}
                    onChange={handleImageChange}
                    maxSize={5} // 5MB
                    acceptedFormats={['image/jpeg', 'image/png', 'image/webp']}
                    placeholder="Upload employee profile image"
                  />
                </div>
              </div>
            )}

            {currentStep === 'review' && (
              <ReviewStep
                formData={watchedData}
                selectedUser={selectedUser}
                createNewUser={createNewUser}
                selectedTechnologies={selectedTechnologies}
              />
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between mt-8">
          <div>
            {currentStep !== 'user' && (
              <EnhancedButton
                type="button"
                variant="secondary"
                onClick={() => {
                  const steps = ['user', 'details', 'technologies', 'image', 'review'];
                  const currentIndex = steps.indexOf(currentStep);
                  if (currentIndex > 0) {
                    setCurrentStep(steps[currentIndex - 1] as any);
                  }
                }}
                className="flex items-center space-x-2"
              >
                <span>Previous</span>
              </EnhancedButton>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <EnhancedButton
              type="button"
              variant="secondary"
              onClick={onCancel}
            >
              Cancel
            </EnhancedButton>

            {currentStep === 'review' ? (
              <EnhancedButton
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className="flex items-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <FiUserPlus className="w-4 h-4" />
                    <span>Create Employee</span>
                  </>
                )}
              </EnhancedButton>
            ) : (
              <EnhancedButton
                type="button"
                variant="primary"
                onClick={() => {
                  const steps = ['user', 'details', 'technologies', 'image', 'review'];
                  const currentIndex = steps.indexOf(currentStep);

                  // Validation logic
                  if (currentStep === 'user' && !canProceedToDetails) {
                    toast.error('Please select a user or choose to create a new one');
                    return;
                  }
                  if (currentStep === 'details' && !canProceedToTechnologies) {
                    toast.error('Please fill in all required fields');
                    return;
                  }

                  if (currentIndex < steps.length - 1) {
                    setCurrentStep(steps[currentIndex + 1] as any);
                  }
                }}
                disabled={
                  (currentStep === 'user' && !canProceedToDetails) ||
                  (currentStep === 'details' && !canProceedToTechnologies)
                }
              >
                Next
              </EnhancedButton>
            )}
          </div>
        </div>
      </form>

      {/* Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirm Employee Creation"
        size="lg"
      >
        <div className="space-y-6">
          <div className="flex items-center space-x-3 p-4 bg-primary-500/10 rounded-lg border border-primary-500/20">
            <FiAlertCircle className="w-6 h-6 text-primary-400 flex-shrink-0" />
            <div>
              <h4 className="text-white font-medium">Ready to Create Employee</h4>
              <p className="text-primary-300 text-sm mt-1">
                Please review the information below before creating the employee record.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Employee Information */}
            <div className="space-y-4">
              <h5 className="text-white font-medium flex items-center space-x-2">
                <FiUser className="w-4 h-4" />
                <span>Employee Information</span>
              </h5>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-primary-300">Name:</span>
                  <span className="text-white">{watchedData.firstName} {watchedData.lastName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-primary-300">Email:</span>
                  <span className="text-white">{watchedData.email}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-primary-300">Position:</span>
                  <span className="text-white">{watchedData.position}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-primary-300">Department:</span>
                  <span className="text-white">{DEPARTMENT_OPTIONS.find(d => d.value === watchedData.department)?.label}</span>
                </div>
              </div>
            </div>

            {/* User Linking */}
            <div className="space-y-4">
              <h5 className="text-white font-medium flex items-center space-x-2">
                <FiUserPlus className="w-4 h-4" />
                <span>User Account</span>
              </h5>
              <div className="space-y-2 text-sm">
                {selectedUser ? (
                  <>
                    <div className="flex items-center space-x-2 text-green-400">
                      <FiCheck className="w-4 h-4" />
                      <span>Linking to existing user</span>
                    </div>
                    <div className="text-primary-300">
                      User: {selectedUser.firstName} {selectedUser.lastName}
                    </div>
                  </>
                ) : createNewUser ? (
                  <>
                    <div className="flex items-center space-x-2 text-blue-400">
                      <FiPlus className="w-4 h-4" />
                      <span>Creating new user account</span>
                    </div>
                    <div className="text-primary-300">
                      Email: {watchedData.email}
                    </div>
                  </>
                ) : (
                  <div className="text-yellow-400">
                    No user account will be created
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Technologies */}
          {selectedTechnologies.length > 0 && (
            <div className="space-y-4">
              <h5 className="text-white font-medium flex items-center space-x-2">
                <FiCode className="w-4 h-4" />
                <span>Technologies ({selectedTechnologies.length})</span>
              </h5>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {selectedTechnologies.map((tech) => (
                  <div key={tech.technologyId} className="flex items-center justify-between p-3 bg-dark-800/50 rounded-lg">
                    <span className="text-white text-sm">{tech.technology.name}</span>
                    <span className="text-primary-400 text-sm font-medium">{tech.proficiencyLevel}%</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-dark-700">
            <EnhancedButton
              type="button"
              variant="secondary"
              onClick={() => setShowConfirmModal(false)}
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton
              type="button"
              variant="primary"
              onClick={handleConfirmSubmit}
              disabled={isSubmitting}
              className="flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <FiCheck className="w-4 h-4" />
                  <span>Confirm & Create</span>
                </>
              )}
            </EnhancedButton>
          </div>
        </div>
      </Modal>
    </div>
  );
};
