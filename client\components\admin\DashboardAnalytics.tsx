'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiUsers,
  FiFolderPlus,
  FiCode,
  FiActivity,
  FiTrendingUp,
  FiTrendingDown,
  FiClock,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';
import { Card, CardHeader, CardBody } from '@/components/shared/Card';
import { StatCard, MetricCard } from '@/components/shared/Card';
import { Spinner } from '@/components/shared/Spinner';
import { Alert } from '@/components/shared/Alert';

interface DashboardAnalyticsData {
  employees: {
    total: number;
    active: number;
    newThisMonth: number;
    trend: number;
  };
  projects: {
    total: number;
    active: number;
    completed: number;
    overdue: number;
    trend: number;
  };
  tasks: {
    total: number;
    completed: number;
    inProgress: number;
    pending: number;
    completionRate: number;
  };
  technologies: {
    total: number;
    trending: string[];
    mostUsed: string[];
  };
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: string;
    responseTime: number;
    errorRate: number;
  };
  recentActivity: {
    employeesJoined: number;
    projectsCreated: number;
    tasksCompleted: number;
    lastUpdated: string;
  };
}

interface DashboardAnalyticsProps {
  userRole?: string;
  className?: string;
}

export const DashboardAnalytics: React.FC<DashboardAnalyticsProps> = ({
  userRole = 'ADMIN',
  className
}) => {
  const [data, setData] = useState<DashboardAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        
        // TODO: Replace with actual API call
        // const response = await fetch('/api/admin/analytics');
        // const analyticsData = await response.json();
        
        // Mock data for now
        const mockData: DashboardAnalyticsData = {
          employees: {
            total: 156,
            active: 142,
            newThisMonth: 12,
            trend: 8.5
          },
          projects: {
            total: 34,
            active: 23,
            completed: 8,
            overdue: 3,
            trend: 12.3
          },
          tasks: {
            total: 287,
            completed: 198,
            inProgress: 67,
            pending: 22,
            completionRate: 69
          },
          technologies: {
            total: 45,
            trending: ['React', 'TypeScript', 'Next.js'],
            mostUsed: ['JavaScript', 'Python', 'Node.js']
          },
          systemHealth: {
            status: 'healthy',
            uptime: '99.9%',
            responseTime: 245,
            errorRate: 0.02
          },
          recentActivity: {
            employeesJoined: 5,
            projectsCreated: 3,
            tasksCompleted: 47,
            lastUpdated: new Date().toISOString()
          }
        };
        
        setData(mockData);
      } catch (err) {
        setError('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        type="error"
        message={error}
        closable
        onClose={() => setError(null)}
      />
    );
  }

  if (!data) return null;

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'critical': return 'red';
      default: return 'gray';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <FiCheckCircle className="w-5 h-5" />;
      case 'warning': return <FiAlertCircle className="w-5 h-5" />;
      case 'critical': return <FiAlertCircle className="w-5 h-5" />;
      default: return <FiActivity className="w-5 h-5" />;
    }
  };

  return (
    <div className={className}>
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <StatCard
            title="Total Employees"
            value={data.employees.total}
            icon={<FiUsers className="w-5 h-5" />}
            change={{
              value: data.employees.trend,
              type: data.employees.trend > 0 ? 'increase' : data.employees.trend < 0 ? 'decrease' : 'neutral'
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <StatCard
            title="Active Projects"
            value={data.projects.active}
            icon={<FiFolderPlus className="w-5 h-5" />}
            change={{
              value: data.projects.trend,
              type: data.projects.trend > 0 ? 'increase' : data.projects.trend < 0 ? 'decrease' : 'neutral'
            }}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <StatCard
            title="Task Completion"
            value={`${data.tasks.completionRate}%`}
            icon={<FiCheckCircle className="w-5 h-5" />}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <StatCard
            title="System Health"
            value={data.systemHealth.uptime}
            icon={getHealthIcon(data.systemHealth.status)}
          />
        </motion.div>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Project Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card>
            <CardHeader title="Project Status" />
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Active</span>
                  <span className="font-medium">{data.projects.active}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  <span className="font-medium text-green-600">{data.projects.completed}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Overdue</span>
                  <span className="font-medium text-red-600">{data.projects.overdue}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Task Breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Card>
            <CardHeader title="Task Breakdown" />
            <CardBody>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                  <span className="font-medium text-green-600">{data.tasks.completed}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">In Progress</span>
                  <span className="font-medium text-blue-600">{data.tasks.inProgress}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                  <span className="font-medium text-yellow-600">{data.tasks.pending}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <Card>
          <CardHeader 
            title="Recent Activity" 
            subtitle={`Last updated: ${new Date(data.recentActivity.lastUpdated).toLocaleString()}`}
          />
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {data.recentActivity.employeesJoined}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">New Employees</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {data.recentActivity.projectsCreated}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Projects Created</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {data.recentActivity.tasksCompleted}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Tasks Completed</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default DashboardAnalytics;
