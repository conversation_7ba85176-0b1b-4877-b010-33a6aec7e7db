'use client';

import React, { useState, useMemo } from 'react';
import { cn } from '@/lib/utils/cn';
import { Button, IconButton } from '@/components/shared/forms/Button';
import { Input, Select, Checkbox } from '@/components/shared/forms/FormField';
import { Spinner, Skeleton } from '@/components/shared/Spinner';

export interface Column<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  className?: string;
}

export interface DataTableProps<T = any> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
    showSizeChanger?: boolean;
    pageSizeOptions?: string[];
  };
  rowSelection?: {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean };
  };
  onRow?: (record: T, index: number) => {
    onClick?: () => void;
    onDoubleClick?: () => void;
    className?: string;
  };
  scroll?: { x?: number; y?: number };
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  showHeader?: boolean;
  className?: string;
  emptyText?: React.ReactNode;
  rowKey?: string | ((record: T) => string);
}

export const DataTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  rowSelection,
  onRow,
  scroll,
  size = 'middle',
  bordered = false,
  showHeader = true,
  className,
  emptyText = 'No data',
  rowKey = 'id',
}: DataTableProps<T>) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [filters, setFilters] = useState<Record<string, string>>({});

  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] || index.toString();
  };

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleFilter = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const sortedAndFilteredData = useMemo(() => {
    let result = [...data];

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        result = result.filter(item => {
          const itemValue = item[key];
          return itemValue?.toString().toLowerCase().includes(value.toLowerCase());
        });
      }
    });

    // Apply sorting
    if (sortConfig) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return result;
  }, [data, sortConfig, filters]);

  const sizeClasses = {
    small: 'text-xs',
    middle: 'text-sm',
    large: 'text-base',
  };

  const cellPadding = {
    small: 'px-2 py-1',
    middle: 'px-4 py-2',
    large: 'px-6 py-3',
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {showHeader && (
          <div className="flex space-x-4">
            {columns.map((column, index) => (
              <Skeleton key={index} className="h-8 flex-1" />
            ))}
          </div>
        )}
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex space-x-4">
            {columns.map((column, colIndex) => (
              <Skeleton key={colIndex} className="h-12 flex-1" />
            ))}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('overflow-hidden', className)}>
      <div className={cn('overflow-auto', scroll?.x && 'min-w-full')}>
        <table className={cn(
          'w-full table-auto',
          sizeClasses[size],
          bordered && 'border border-gray-200 dark:border-gray-700'
        )}>
          {showHeader && (
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                {rowSelection && (
                  <th className={cn(
                    'text-left font-medium text-gray-900 dark:text-gray-100',
                    cellPadding[size],
                    bordered && 'border-b border-gray-200 dark:border-gray-700'
                  )}>
                    <Checkbox
                      checked={
                        sortedAndFilteredData.length > 0 &&
                        sortedAndFilteredData.every(record =>
                          rowSelection.selectedRowKeys.includes(getRowKey(record, 0))
                        )
                      }
                      onChange={(e) => {
                        if (e.target.checked) {
                          const allKeys = sortedAndFilteredData.map((record, index) => 
                            getRowKey(record, index)
                          );
                          rowSelection.onChange(allKeys, sortedAndFilteredData);
                        } else {
                          rowSelection.onChange([], []);
                        }
                      }}
                    />
                  </th>
                )}
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={cn(
                      'text-left font-medium text-gray-900 dark:text-gray-100',
                      cellPadding[size],
                      bordered && 'border-b border-gray-200 dark:border-gray-700',
                      column.align === 'center' && 'text-center',
                      column.align === 'right' && 'text-right',
                      column.className
                    )}
                    style={{ width: column.width }}
                  >
                    <div className="flex items-center space-x-2">
                      <span>{column.title}</span>
                      {column.sortable && (
                        <button
                          onClick={() => handleSort(column.key)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {sortConfig?.key === column.key ? (
                            sortConfig.direction === 'asc' ? '↑' : '↓'
                          ) : (
                            '↕'
                          )}
                        </button>
                      )}
                    </div>
                    {column.filterable && (
                      <div className="mt-1">
                        <Input
                          placeholder={`Filter ${column.title}`}
                          value={filters[column.key] || ''}
                          onChange={(e) => handleFilter(column.key, e.target.value)}
                          className="text-xs"
                        />
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
          )}
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {sortedAndFilteredData.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (rowSelection ? 1 : 0)}
                  className={cn(
                    'text-center text-gray-500 dark:text-gray-400',
                    cellPadding[size]
                  )}
                >
                  {emptyText}
                </td>
              </tr>
            ) : (
              sortedAndFilteredData.map((record, index) => {
                const rowProps = onRow?.(record, index) || {};
                const key = getRowKey(record, index);
                
                return (
                  <tr
                    key={key}
                    className={cn(
                      'hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
                      rowProps.className
                    )}
                    onClick={rowProps.onClick}
                    onDoubleClick={rowProps.onDoubleClick}
                  >
                    {rowSelection && (
                      <td className={cn(
                        cellPadding[size],
                        bordered && 'border-b border-gray-200 dark:border-gray-700'
                      )}>
                        <Checkbox
                          checked={rowSelection.selectedRowKeys.includes(key)}
                          onChange={(e) => {
                            const selectedKeys = e.target.checked
                              ? [...rowSelection.selectedRowKeys, key]
                              : rowSelection.selectedRowKeys.filter(k => k !== key);
                            const selectedRows = sortedAndFilteredData.filter((r, i) =>
                              selectedKeys.includes(getRowKey(r, i))
                            );
                            rowSelection.onChange(selectedKeys, selectedRows);
                          }}
                          {...(rowSelection.getCheckboxProps?.(record) || {})}
                        />
                      </td>
                    )}
                    {columns.map((column) => {
                      const value = column.dataIndex ? record[column.dataIndex] : record[column.key];
                      const content = column.render
                        ? column.render(value, record, index)
                        : value?.toString() || '';

                      return (
                        <td
                          key={column.key}
                          className={cn(
                            'text-gray-900 dark:text-gray-100',
                            cellPadding[size],
                            bordered && 'border-b border-gray-200 dark:border-gray-700',
                            column.align === 'center' && 'text-center',
                            column.align === 'right' && 'text-right',
                            column.className
                          )}
                        >
                          {content}
                        </td>
                      );
                    })}
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="flex items-center justify-between mt-4 px-4 py-3 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
            <span>
              Showing {((pagination.current - 1) * pagination.pageSize) + 1} to{' '}
              {Math.min(pagination.current * pagination.pageSize, pagination.total)} of{' '}
              {pagination.total} entries
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {pagination.showSizeChanger && (
              <Select
                value={pagination.pageSize.toString()}
                onChange={(e) => pagination.onChange(1, parseInt(e.target.value))}
                options={
                  pagination.pageSizeOptions?.map(size => ({
                    value: size,
                    label: `${size} / page`
                  })) || [
                    { value: '10', label: '10 / page' },
                    { value: '20', label: '20 / page' },
                    { value: '50', label: '50 / page' },
                    { value: '100', label: '100 / page' },
                  ]
                }
                className="w-32"
              />
            )}
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.current <= 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {pagination.current} of {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
