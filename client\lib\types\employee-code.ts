// Employee Code Management Types
export enum EmployeeCodeRole {
  EMPLOYEE = 'EMPLOYEE',
  ADMIN = 'ADMIN',
  TEAM_LEADER = 'TEAM_LEADER',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
}

export interface EmployeeCode {
  id: string;
  code: string;
  description: string;
  role: EmployeeCodeRole;
  isUsed: boolean;
  usedBy?: string;
  usedAt?: Date;
  createdBy: string;
  expiresAt?: Date;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateEmployeeCodeDto {
  code: string;
  description: string;
  role: EmployeeCodeRole;
  expiresAt?: Date;
  notes?: string;
}

export interface UpdateEmployeeCodeDto {
  description?: string;
  role?: EmployeeCodeRole;
  expiresAt?: Date;
  isActive?: boolean;
  notes?: string;
}

export interface EmployeeCodeFilters {
  search?: string;
  role?: EmployeeCodeRole;
  isUsed?: boolean;
  isActive?: boolean;
  createdBy?: string;
  page?: number;
  limit?: number;
}

export interface EmployeeCodeStats {
  total: number;
  used: number;
  unused: number;
  expired: number;
  active: number;
  byRole: { [key: string]: number };
}

// Bulk operations
export interface BulkGenerateCodesDto {
  count: number;
  role: EmployeeCodeRole;
  description: string;
  expiresAt?: Date;
  prefix?: string;
}

export interface BulkUpdateCodesDto {
  ids: string[];
  isActive?: boolean;
  expiresAt?: Date;
}

export interface BulkDeleteCodesDto {
  ids: string[];
}
