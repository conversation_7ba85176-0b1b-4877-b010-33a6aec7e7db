import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminProjectsApi } from '@/lib/api/admin/projects';
import { Project, CreateProjectDto, UpdateProjectDto, ProjectFilters } from '@/lib/types/project';
import { toast } from 'react-hot-toast';

// Query keys
export const projectKeys = {
  all: ['projects'] as const,
  lists: () => [...projectKeys.all, 'list'] as const,
  list: (filters: ProjectFilters) => [...projectKeys.lists(), filters] as const,
  details: () => [...projectKeys.all, 'detail'] as const,
  detail: (id: string) => [...projectKeys.details(), id] as const,
  stats: () => [...projectKeys.all, 'stats'] as const,
  analytics: (id: string) => [...projectKeys.all, 'analytics', id] as const,
  timeline: (id: string) => [...projectKeys.all, 'timeline', id] as const,
  tasks: (id: string) => [...projectKeys.all, 'tasks', id] as const,
  templates: () => [...projectKeys.all, 'templates'] as const,
};

// Queries
export const useProjects = (filters: ProjectFilters = {}) => {
  return useQuery({
    queryKey: projectKeys.list(filters),
    queryFn: () => adminProjectsApi.getAll(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProject = (id: string) => {
  return useQuery({
    queryKey: projectKeys.detail(id),
    queryFn: () => adminProjectsApi.getById(id),
    enabled: !!id,
  });
};

export const useProjectStats = () => {
  return useQuery({
    queryKey: projectKeys.stats(),
    queryFn: () => adminProjectsApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useProjectAnalytics = (id: string) => {
  return useQuery({
    queryKey: projectKeys.analytics(id),
    queryFn: () => adminProjectsApi.getProjectAnalytics(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useProjectTimeline = (id: string) => {
  return useQuery({
    queryKey: projectKeys.timeline(id),
    queryFn: () => adminProjectsApi.getTimeline(id),
    enabled: !!id,
  });
};

export const useProjectTasks = (id: string) => {
  return useQuery({
    queryKey: projectKeys.tasks(id),
    queryFn: () => adminProjectsApi.getTasks(id),
    enabled: !!id,
  });
};

export const useProjectTemplates = () => {
  return useQuery({
    queryKey: projectKeys.templates(),
    queryFn: () => adminProjectsApi.getTemplates(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Mutations
export const useCreateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProjectDto) => adminProjectsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.stats() });
      toast.success('Project created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create project');
    },
  });
};

export const useUpdateProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProjectDto }) =>
      adminProjectsApi.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: projectKeys.stats() });
      toast.success('Project updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update project');
    },
  });
};

export const useDeleteProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminProjectsApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.stats() });
      toast.success('Project deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete project');
    },
  });
};

export const useBulkDeleteProjects = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => adminProjectsApi.bulkDelete(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.stats() });
      toast.success('Projects deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete projects');
    },
  });
};

export const useAddProjectMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, memberData }: { projectId: string; memberData: any }) =>
      adminProjectsApi.addMember(projectId, memberData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(variables.projectId) });
      toast.success('Member added successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to add member');
    },
  });
};

export const useRemoveProjectMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, userId }: { projectId: string; userId: string }) =>
      adminProjectsApi.removeMember(projectId, userId),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(variables.projectId) });
      toast.success('Member removed successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to remove member');
    },
  });
};

export const useUploadProjectImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ projectId, file }: { projectId: string; file: File }) =>
      adminProjectsApi.uploadImage(projectId, file),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(variables.projectId) });
      toast.success('Image uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to upload image');
    },
  });
};

export const useToggleProjectStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, action }: { id: string; action: 'activate' | 'deactivate' | 'feature' | 'unfeature' }) => {
      switch (action) {
        case 'activate':
          return adminProjectsApi.activate(id);
        case 'deactivate':
          return adminProjectsApi.deactivate(id);
        case 'feature':
          return adminProjectsApi.feature(id);
        case 'unfeature':
          return adminProjectsApi.unfeature(id);
        default:
          throw new Error('Invalid action');
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: projectKeys.stats() });
      toast.success('Project status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update project status');
    },
  });
};
