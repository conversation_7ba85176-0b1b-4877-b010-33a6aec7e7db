'use client';

import React from 'react';
import { cn } from '@/lib/utils/cn';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

const badgeVariants = {
  default: 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300',
  secondary: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  success: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
  warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
  danger: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  info: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
};

const badgeSizes = {
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-1 text-sm',
  lg: 'px-3 py-1.5 text-base',
};

const colorVariants: Record<string, string> = {
  blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
  green: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
  yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
  red: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  purple: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
  pink: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300',
  gray: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300',
  orange: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
  emerald: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-300',
};

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  color,
  className,
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';
  
  let variantClass = badgeVariants[variant];
  if (color && colorVariants[color]) {
    variantClass = colorVariants[color];
  }

  return (
    <span
      className={cn(
        baseClasses,
        variantClass,
        badgeSizes[size],
        className
      )}
    >
      {children}
    </span>
  );
};
