# 🧩 Project System Architecture (Overview)

## Summary

This document describes how all systems integrate and interact in a modular architecture with a **Next.js 15 + TypeScript** frontend, using **TailwindCSS v4** for UI, and **MongoDB** for persistence.

Each system is self-contained but shares models or relations through clear interfaces and service contracts.

## System Integration Diagram

```
[Employee Code System] ---> [Authentication System] ---> [Employee Management System]
                                          |                            |
                                          v                            v
                                  [Admin Panel]                 [Project System] 
                                          |                            |
                                          v                            v
                                 [Technology System] <--> [Task System]
```

## System Roles

| System              | Description                                  | Owner                   |
| ------------------- | -------------------------------------------- | ----------------------- |
| Authentication      | Handles OTP-based sign up and login          | Public / Auth           |
| Employee Code       | Controls which codes are allowed to register | Admin Only              |
| Employee Management | Full profile and HR-level detail             | Admin / Employee        |
| Technology          | Manages tech stack and employee skills       | Admin Only              |
| Project             | Core of company activity tracking            | Admin / Project Manager |
| Task                | Detailed work units per project              | Admin / Team Leader     |

## Key Concepts

- **Loose Coupling**: Each system operates independently and is integrated only where necessary.
- **Strict Validation**: All user input across modules is validated (frontend and backend).
- **Single Source of Truth**: MongoDB manages all canonical data.
- **Role-Based Access Control**: Admin vs Employee vs PM vs Team Leader roles.

## Development Standards

- Modular folders per domain.
- API routes isolated under `/api/` in Next.js.
- Admin Panel uses server-side rendering (SSR) where needed.
- Shared types/interfaces in `/lib/types` or `/lib/schemas`.

---

This architecture ensures easy future extensibility (e.g., Payroll, Performance, Leave Requests) with plug-and-play modules.

