'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { FiArrowLeft, FiFolder, FiSave, FiX } from 'react-icons/fi';
import { useProject, useUpdateProject } from '@/lib/hooks/use-projects';
import { useEmployees } from '@/lib/hooks/use-employees';
import { useTechnologies } from '@/lib/hooks/use-technologies';
import { UpdateProjectDto, ProjectStatus, ProjectPriority, ProjectMemberRole } from '@/lib/types/project';
import { Button } from '@/components/shared/forms/Button';
import { GlassCard } from '@/components/ui/glass-card';
import { Alert } from '@/components/shared/Alert';
import { Spinner } from '@/components/shared/Spinner';

export default function EditProjectPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const { data: project, isLoading: projectLoading, error } = useProject(projectId);
  const updateProjectMutation = useUpdateProject();
  const { data: employeesData, isLoading: employeesLoading } = useEmployees({ limit: 1000 });
  const { data: technologiesData, isLoading: technologiesLoading } = useTechnologies({ limit: 1000 });

  const employees = employeesData?.employees || [];
  const technologies = technologiesData?.technologies || [];

  const [formData, setFormData] = useState<UpdateProjectDto>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedMembers, setSelectedMembers] = useState<Array<{ userId: string; role: ProjectMemberRole }>>([]);
  const [selectedTechnologies, setSelectedTechnologies] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Extract project data from API response
  const projectData = project?.data;

  // Initialize form data when project loads
  useEffect(() => {
    if (projectData) {
      const formDataUpdate: any = {
        name: projectData.name,
        description: projectData.description || '',
        longDescription: projectData.longDescription || '',
        status: projectData.status,
        priority: projectData.priority,
        progress: projectData.progress,
        tags: projectData.tags || [],
        clientName: projectData.clientName || '',
        clientEmail: projectData.clientEmail || '',
        clientPhone: projectData.clientPhone || '',
        repositoryUrl: projectData.repositoryUrl || '',
        liveUrl: projectData.liveUrl || '',
        demoUrl: projectData.demoUrl || '',
        isActive: projectData.isActive,
        isFeatured: projectData.isFeatured,
      };

      // Add optional date fields only if they exist
      if (projectData.startDate) {
        formDataUpdate.startDate = new Date(projectData.startDate);
      }
      if (projectData.endDate) {
        formDataUpdate.endDate = new Date(projectData.endDate);
      }
      if (projectData.deadline) {
        formDataUpdate.deadline = new Date(projectData.deadline);
      }
      if (projectData.budget !== undefined) {
        formDataUpdate.budget = projectData.budget;
      }
      if (projectData.currency) {
        formDataUpdate.currency = projectData.currency;
      }
      if (projectData.projectManager) {
        formDataUpdate.projectManager = projectData.projectManager;
      }

      setFormData(formDataUpdate);

      setSelectedMembers(projectData.members || []);
      setSelectedTechnologies(projectData.technologies || []);
    }
  }, [projectData]);

  const handleInputChange = (field: keyof UpdateProjectDto, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddMember = (userId: string, role: ProjectMemberRole) => {
    if (!selectedMembers.find(m => m.userId === userId)) {
      setSelectedMembers(prev => [...prev, { userId, role }]);
    }
  };

  const handleRemoveMember = (userId: string) => {
    setSelectedMembers(prev => prev.filter(m => m.userId !== userId));
  };

  const handleUpdateMemberRole = (userId: string, role: ProjectMemberRole) => {
    setSelectedMembers(prev => 
      prev.map(m => m.userId === userId ? { ...m, role } : m)
    );
  };

  const handleAddTechnology = (technologyId: string) => {
    if (!selectedTechnologies.includes(technologyId)) {
      setSelectedTechnologies(prev => [...prev, technologyId]);
    }
  };

  const handleRemoveTechnology = (technologyId: string) => {
    setSelectedTechnologies(prev => prev.filter(id => id !== technologyId));
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags?.includes(tagInput.trim())) {
      handleInputChange('tags', [...(formData.tags || []), tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    handleInputChange('tags', formData.tags?.filter(t => t !== tag) || []);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Project name is required';
    }

    if (!formData.projectManager) {
      newErrors.projectManager = 'Project manager is required';
    }

    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (formData.budget && formData.budget < 0) {
      newErrors.budget = 'Budget must be a positive number';
    }

    if (formData.progress !== undefined && (formData.progress < 0 || formData.progress > 100)) {
      newErrors.progress = 'Progress must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const updateData: UpdateProjectDto = {
        ...formData,
        members: selectedMembers.map(member => ({
          ...member,
          joinedAt: new Date()
        })),
        technologies: selectedTechnologies,
      };

      await updateProjectMutation.mutateAsync({ id: projectId, data: updateData });
      router.push(`/admin/projects/${projectId}`);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleCancel = () => {
    router.push(`/admin/projects/${projectId}`);
  };

  if (projectLoading || employeesLoading || technologiesLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-4xl mx-auto">
          <Alert
            type="error"
            title="Error loading project"
            message={error instanceof Error ? error.message : 'Project not found'}
            actions={
              <Link href="/admin/projects">
                <Button variant="primary">Back to Projects</Button>
              </Link>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={`/admin/projects/${projectId}`}>
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center space-x-2"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span>Back to Project</span>
              </Button>
            </Link>
            
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-primary-500 to-accent-purple rounded-xl">
                <FiFolder className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">Edit Project: {projectData?.name}</h1>
                <p className="text-primary-300 mt-1">Update project information and settings</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Project Name *
                </label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`w-full px-4 py-2 bg-dark-700 border rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.name ? 'border-red-500' : 'border-dark-600'
                  }`}
                  placeholder="Enter project name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Short Description
                </label>
                <textarea
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Brief project description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Status
                </label>
                <select
                  value={formData.status || ''}
                  onChange={(e) => handleInputChange('status', e.target.value as ProjectStatus)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value={ProjectStatus.PLANNING}>Planning</option>
                  <option value={ProjectStatus.IN_PROGRESS}>In Progress</option>
                  <option value={ProjectStatus.ON_HOLD}>On Hold</option>
                  <option value={ProjectStatus.COMPLETED}>Completed</option>
                  <option value={ProjectStatus.CANCELLED}>Cancelled</option>
                  <option value={ProjectStatus.ARCHIVED}>Archived</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Priority
                </label>
                <select
                  value={formData.priority || ''}
                  onChange={(e) => handleInputChange('priority', e.target.value as ProjectPriority)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value={ProjectPriority.LOW}>Low</option>
                  <option value={ProjectPriority.MEDIUM}>Medium</option>
                  <option value={ProjectPriority.HIGH}>High</option>
                  <option value={ProjectPriority.URGENT}>Urgent</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Project Manager *
                </label>
                <select
                  value={formData.projectManager || ''}
                  onChange={(e) => handleInputChange('projectManager', e.target.value)}
                  className={`w-full px-4 py-2 bg-dark-700 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.projectManager ? 'border-red-500' : 'border-dark-600'
                  }`}
                >
                  <option value="">Select project manager</option>
                  {employees.map((employee: any) => (
                    <option key={employee.id} value={employee.id}>
                      {employee.firstName} {employee.lastName} - {employee.position}
                    </option>
                  ))}
                </select>
                {errors.projectManager && (
                  <p className="mt-1 text-sm text-red-400">{errors.projectManager}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Progress (%)
                </label>
                <input
                  type="number"
                  value={formData.progress || 0}
                  onChange={(e) => handleInputChange('progress', parseInt(e.target.value) || 0)}
                  className={`w-full px-4 py-2 bg-dark-700 border rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.progress ? 'border-red-500' : 'border-dark-600'
                  }`}
                  placeholder="0"
                  min="0"
                  max="100"
                />
                {errors.progress && (
                  <p className="mt-1 text-sm text-red-400">{errors.progress}</p>
                )}
              </div>
            </div>
          </GlassCard>

          {/* Budget */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Budget</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Budget Amount
                </label>
                <input
                  type="number"
                  value={formData.budget || ''}
                  onChange={(e) => handleInputChange('budget', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className={`w-full px-4 py-2 bg-dark-700 border rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.budget ? 'border-red-500' : 'border-dark-600'
                  }`}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
                {errors.budget && (
                  <p className="mt-1 text-sm text-red-400">{errors.budget}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Currency
                </label>
                <select
                  value={formData.currency || 'USD'}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                </select>
              </div>
            </div>
          </GlassCard>

          {/* Timeline */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Timeline</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  value={formData.startDate ? new Date(formData.startDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => handleInputChange('startDate', e.target.value ? new Date(e.target.value) : undefined)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  value={formData.endDate ? new Date(formData.endDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => handleInputChange('endDate', e.target.value ? new Date(e.target.value) : undefined)}
                  className={`w-full px-4 py-2 bg-dark-700 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                    errors.endDate ? 'border-red-500' : 'border-dark-600'
                  }`}
                />
                {errors.endDate && (
                  <p className="mt-1 text-sm text-red-400">{errors.endDate}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Deadline
                </label>
                <input
                  type="date"
                  value={formData.deadline ? new Date(formData.deadline).toISOString().split('T')[0] : ''}
                  onChange={(e) => handleInputChange('deadline', e.target.value ? new Date(e.target.value) : undefined)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          </GlassCard>

          {/* Team Members */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Team Members</h2>

            <div className="space-y-4">
              <div className="flex space-x-4">
                <select
                  className="flex-1 px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                  onChange={(e) => {
                    if (e.target.value) {
                      handleAddMember(e.target.value, ProjectMemberRole.MEMBER);
                      e.target.value = '';
                    }
                  }}
                >
                  <option value="">Add team member</option>
                  {employees
                    .filter(emp => !selectedMembers.find(m => m.userId === emp._id))
                    .map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.firstName} {employee.lastName} - {employee.position}
                      </option>
                    ))}
                </select>
              </div>

              {selectedMembers.length > 0 && (
                <div className="space-y-2">
                  {selectedMembers.map(member => {
                    const employee = employees.find((emp: any) => emp._id === member.userId);
                    if (!employee) return null;

                    return (
                      <div key={member.userId} className="flex items-center justify-between p-3 bg-dark-800 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-accent-purple flex items-center justify-center text-white text-sm font-semibold">
                            {employee.firstName[0]}{employee.lastName[0]}
                          </div>
                          <div>
                            <p className="text-white font-medium">
                              {employee.firstName} {employee.lastName}
                            </p>
                            <p className="text-sm text-primary-300">{employee.position}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <select
                            value={member.role}
                            onChange={(e) => handleUpdateMemberRole(member.userId, e.target.value as ProjectMemberRole)}
                            className="px-3 py-1 bg-dark-700 border border-dark-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                          >
                            <option value={ProjectMemberRole.MEMBER}>Member</option>
                            <option value={ProjectMemberRole.LEAD}>Lead</option>
                            <option value={ProjectMemberRole.CONTRIBUTOR}>Contributor</option>
                            <option value={ProjectMemberRole.REVIEWER}>Reviewer</option>
                          </select>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveMember(member.userId)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <FiX className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </GlassCard>

          {/* Technologies */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Technologies</h2>

            <div className="space-y-4">
              <select
                className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                onChange={(e) => {
                  if (e.target.value) {
                    handleAddTechnology(e.target.value);
                    e.target.value = '';
                  }
                }}
              >
                <option value="">Add technology</option>
                {technologies
                  .filter(tech => !selectedTechnologies.includes(tech._id))
                  .map(technology => (
                    <option key={technology.id} value={technology.id}>
                      {technology.name}
                    </option>
                  ))}
              </select>

              {selectedTechnologies.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedTechnologies.map(techId => {
                    const technology = technologies.find((tech: any) => tech._id === techId);
                    if (!technology) return null;

                    return (
                      <div key={techId} className="flex items-center space-x-2 px-3 py-1 bg-dark-800 rounded-full">
                        {technology.icon && (
                          <img src={technology.icon} alt={technology.name} className="w-4 h-4" />
                        )}
                        <span className="text-sm text-white">{technology.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveTechnology(techId)}
                          className="text-red-400 hover:text-red-300 p-0 w-4 h-4"
                        >
                          <FiX className="w-3 h-3" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </GlassCard>

          {/* Tags */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Tags</h2>

            <div className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Add tag and press Enter"
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                >
                  Add
                </Button>
              </div>

              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map(tag => (
                    <div key={tag} className="flex items-center space-x-2 px-3 py-1 bg-primary-500/20 rounded-full">
                      <span className="text-sm text-primary-300">{tag}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveTag(tag)}
                        className="text-red-400 hover:text-red-300 p-0 w-4 h-4"
                      >
                        <FiX className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </GlassCard>

          {/* Client Information */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Client Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Client Name
                </label>
                <input
                  type="text"
                  value={formData.clientName || ''}
                  onChange={(e) => handleInputChange('clientName', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Client or company name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Client Email
                </label>
                <input
                  type="email"
                  value={formData.clientEmail || ''}
                  onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Client Phone
                </label>
                <input
                  type="tel"
                  value={formData.clientPhone || ''}
                  onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="+****************"
                />
              </div>
            </div>
          </GlassCard>

          {/* Project Links */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Project Links</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Repository URL
                </label>
                <input
                  type="url"
                  value={formData.repositoryUrl || ''}
                  onChange={(e) => handleInputChange('repositoryUrl', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="https://github.com/..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Live URL
                </label>
                <input
                  type="url"
                  value={formData.liveUrl || ''}
                  onChange={(e) => handleInputChange('liveUrl', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="https://example.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-300 mb-2">
                  Demo URL
                </label>
                <input
                  type="url"
                  value={formData.demoUrl || ''}
                  onChange={(e) => handleInputChange('demoUrl', e.target.value)}
                  className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="https://demo.example.com"
                />
              </div>
            </div>
          </GlassCard>

          {/* Detailed Description */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Detailed Description</h2>

            <textarea
              value={formData.longDescription || ''}
              onChange={(e) => handleInputChange('longDescription', e.target.value)}
              rows={6}
              className="w-full px-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Detailed project description, goals, requirements, and specifications..."
            />
          </GlassCard>

          {/* Project Settings */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-semibold text-white mb-6">Project Settings</h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-white">Active Project</label>
                  <p className="text-sm text-primary-300">Enable this project to be visible and accessible</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.isActive || false}
                  onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  className="w-4 h-4 text-primary-500 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-white">Featured Project</label>
                  <p className="text-sm text-primary-300">Highlight this project in featured sections</p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.isFeatured || false}
                  onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                  className="w-4 h-4 text-primary-500 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
                />
              </div>
            </div>
          </GlassCard>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="secondary"
              onClick={handleCancel}
              disabled={updateProjectMutation.isPending}
            >
              <FiX className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={updateProjectMutation.isPending}
            >
              {updateProjectMutation.isPending ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <FiSave className="w-4 h-4 mr-2" />
                  Update Project
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
