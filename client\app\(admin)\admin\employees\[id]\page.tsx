'use client';

import React from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiEdit, FiTrash2, FiUser } from 'react-icons/fi';
import { useEmployee, useDeleteEmployee } from '@/lib/hooks/use-employees';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { GlassCard } from '@/components/ui/glass-card';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { EmployeeDetailView } from '../components/EmployeeDetailView';
import toast from 'react-hot-toast';

export default function EmployeeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const employeeId = params.id as string;
  
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  
  const { data: employee, isLoading, error } = useEmployee(employeeId);
  const deleteEmployeeMutation = useDeleteEmployee();

  const handleEdit = () => {
    router.push(`/admin/employees/edit/${employeeId}`);
  };

  const handleDelete = async () => {
    try {
      await deleteEmployeeMutation.mutateAsync(employeeId);
      toast.success('Employee deleted successfully');
      router.push('/admin/employees');
    } catch (error) {
      toast.error('Failed to delete employee');
    }
  };

  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 p-4 sm:p-6">
        <div className="mx-auto max-w-6xl">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-primary-300">Loading employee details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !employee) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 p-4 sm:p-6">
        <div className="mx-auto max-w-6xl">
          <Alert
            type="error"
            title="Employee not found"
            message={error instanceof Error ? error.message : 'The requested employee could not be found.'}
            actions={
              <Button onClick={handleBack} variant="primary" size="sm">
                Go Back
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  const fullName = `${employee.firstName} ${employee.lastName}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 p-4 sm:p-6">
      <div className="mx-auto max-w-6xl space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between"
        >
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={handleBack}
              className="flex items-center space-x-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="rounded-xl bg-gradient-to-br from-primary-500 to-accent-purple p-3 shadow-glow">
                <FiUser className="size-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white sm:text-3xl">{fullName}</h1>
                <p className="mt-1 text-sm text-primary-300 sm:text-base">Employee Details</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="secondary"
              onClick={handleEdit}
              className="flex items-center space-x-2"
            >
              <FiEdit className="w-4 h-4" />
              <span>Edit Employee</span>
            </Button>
            
            <Button
              variant="secondary"
              onClick={() => setIsDeleteDialogOpen(true)}
              className="flex items-center space-x-2 bg-red-500/20 border-red-500 text-red-400 hover:bg-red-500/30"
            >
              <FiTrash2 className="w-4 h-4" />
              <span>Delete</span>
            </Button>
          </div>
        </motion.div>

        {/* Employee Detail View */}
        <EmployeeDetailView employee={employee} />
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Employee"
        message={`Are you sure you want to delete ${fullName}? This action cannot be undone.`}
        confirmText="Delete Employee"
        cancelText="Cancel"
        variant="danger"
        isLoading={deleteEmployeeMutation.isPending}
      />
    </div>
  );
}
