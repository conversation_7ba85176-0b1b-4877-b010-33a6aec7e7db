const { MongoClient } = require('mongodb');

async function testEmployeeCreation() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    const db = client.db('silla_link');
    
    console.log('🔍 Checking current database state...\n');
    
    // Check existing users
    const users = await db.collection('users').find({}, {
      projection: { email: 1, firstName: 1, lastName: 1, role: 1 }
    }).toArray();
    console.log('📊 Current Users:', users.length);
    users.forEach(user => {
      console.log(`  - ${user.firstName} ${user.lastName} (${user.email}) - Role: ${user.role}`);
    });
    
    // Check existing employees
    const employees = await db.collection('employees').find({}, {
      projection: { firstName: 1, lastName: 1, email: 1, employeeId: 1, position: 1, department: 1, userId: 1 }
    }).toArray();
    console.log('\n👥 Current Employees:', employees.length);
    employees.forEach(emp => {
      console.log(`  - ${emp.firstName} ${emp.lastName} (${emp.email}) - ID: ${emp.employeeId} - Position: ${emp.position} - Dept: ${emp.department} - UserID: ${emp.userId}`);
    });
    
    // Check available employee codes
    const employeeCodes = await db.collection('employeeCodes').find({
      isUsed: false,
      isActive: true
    }, {
      projection: { code: 1, role: 1, description: 1, isUsed: 1 }
    }).toArray();
    console.log('\n🎫 Available Employee Codes:', employeeCodes.length);
    employeeCodes.forEach(code => {
      console.log(`  - ${code.code} (${code.role}) - ${code.description} - Used: ${code.isUsed}`);
    });
    
    if (employeeCodes.length > 0) {
      console.log('\n✅ Ready to test employee creation with code:', employeeCodes[0].code);
      console.log('📝 Test user details:');
      console.log('  - Name: Test User');
      console.log('  - Email: <EMAIL>');
      console.log('  - Code:', employeeCodes[0].code);
      console.log('  - Expected Role:', employeeCodes[0].role);
    } else {
      console.log('\n❌ No available employee codes found for testing');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

testEmployeeCreation();
