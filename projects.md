# Project Management System

## Overview
This system allows full control over managing company projects, including details, images, linked technologies, participating employees, and related tasks.

## Features
- Admin can:
  - Create projects.
  - Edit and delete projects.
  - View project dashboard.
- Each project includes:
  - Name
  - Main Image
  - Gallery (optional)
  - Status (e.g., Active, Completed, Paused)
  - Description
  - Linked Technologies (from Technology System)
  - Participating Employees (from Employee Management)
  - Task List (from Task Management)

## Fields
- Name
- Description
- Status (enum)
- Cover Image
- Gallery Images
- Technologies (array of tech refs)
- Employees (array of employee refs)
- Tasks (array of task refs)

## Integration with Other Systems
- **Technology System**: Technologies are linked and counted here.
- **Employee Management**: Each employee is linked to projects.
- **Task System**: Each project contains its own tasks.

## Admin Panel Features
- Assign team members with drag-and-drop (optional enhancement).
- View project progress via task stats.
- Filter/search projects.

## Notes
- Project count per tech auto-updated when linked.
- Deleting a project detaches it from all related techs and employees.

---

**Next**: `TASKS.md`

