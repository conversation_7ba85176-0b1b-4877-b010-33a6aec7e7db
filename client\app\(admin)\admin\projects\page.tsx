'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import { 
  FiPlus, 
  FiSearch, 
  FiFilter, 
  FiDownload, 
  FiGrid, 
  FiList,
  FiFolder,
  FiUsers,
  FiCalendar,
  FiTrendingUp,
  FiRefreshCw,
  FiTrash2,
  FiEdit3,
  FiEye,
  FiMoreVertical,
  FiStar,
  FiClock,
  FiTarget
} from 'react-icons/fi';
import { useProjects, useDeleteProject, useBulkDeleteProjects, useToggleProjectStatus } from '@/lib/hooks/use-projects';
import { useEmployees } from '@/lib/hooks/use-employees';
import { useTechnologies } from '@/lib/hooks/use-technologies';
import { Project, ProjectStatus, ProjectPriority, ProjectFilters } from '@/lib/types/project';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { Spinner } from '@/components/shared/Spinner';
import { GlassCard } from '@/components/ui/glass-card';
import { DataTable } from '@/components/shared/DataTable';
import { Modal } from '@/components/shared/Modal';
import { Badge } from '@/components/shared/Badge';

export default function ProjectsPage() {
  const [filters, setFilters] = useState<ProjectFilters>({
    page: 1,
    limit: 10,
    search: '',
  });

  const [viewMode, setViewMode] = useState<'grid' | 'table'>('table');
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

  // Hooks
  const { data: projectsData, isLoading, error, refetch } = useProjects(filters);
  const { data: employeesData } = useEmployees({ limit: 1000 });
  const { data: technologiesData } = useTechnologies({ limit: 1000 });
  const deleteProjectMutation = useDeleteProject();
  const bulkDeleteMutation = useBulkDeleteProjects();
  const toggleStatusMutation = useToggleProjectStatus();

  const projects = projectsData?.data?.data || [];
  const totalProjects = projectsData?.data?.pagination?.total || 0;
  const employees = employeesData?.employees || [];
  const technologies = technologiesData?.technologies || [];

  // Handlers
  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (key: keyof ProjectFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId) 
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProjects.length === projects.length) {
      setSelectedProjects([]);
    } else {
      setSelectedProjects(projects.map(p => p.id));
    }
  };

  const handleDeleteProject = async () => {
    if (!projectToDelete) return;
    
    try {
      await deleteProjectMutation.mutateAsync(projectToDelete);
      setShowDeleteModal(false);
      setProjectToDelete(null);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleBulkDelete = async () => {
    try {
      await bulkDeleteMutation.mutateAsync(selectedProjects);
      setSelectedProjects([]);
      setShowBulkDeleteModal(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleToggleStatus = async (projectId: string, action: 'activate' | 'deactivate' | 'feature' | 'unfeature') => {
    try {
      await toggleStatusMutation.mutateAsync({ id: projectId, action });
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  // Status and priority options
  const statusOptions = [
    { value: '', header: 'All Statuses' },
    { value: ProjectStatus.PLANNING, header: 'Planning' },
    { value: ProjectStatus.IN_PROGRESS, header: 'In Progress' },
    { value: ProjectStatus.ON_HOLD, header: 'On Hold' },
    { value: ProjectStatus.COMPLETED, header: 'Completed' },
    { value: ProjectStatus.CANCELLED, header: 'Cancelled' },
    { value: ProjectStatus.ARCHIVED, header: 'Archived' },
  ];

  const priorityOptions = [
    { value: '', header: 'All Priorities' },
    { value: ProjectPriority.LOW, header: 'Low' },
    { value: ProjectPriority.MEDIUM, header: 'Medium' },
    { value: ProjectPriority.HIGH, header: 'High' },
    { value: ProjectPriority.URGENT, header: 'Urgent' },
  ];

  // Get status badge color
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case ProjectStatus.PLANNING: return 'blue';
      case ProjectStatus.IN_PROGRESS: return 'green';
      case ProjectStatus.ON_HOLD: return 'yellow';
      case ProjectStatus.COMPLETED: return 'emerald';
      case ProjectStatus.CANCELLED: return 'red';
      case ProjectStatus.ARCHIVED: return 'gray';
      default: return 'gray';
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority: ProjectPriority) => {
    switch (priority) {
      case ProjectPriority.LOW: return 'gray';
      case ProjectPriority.MEDIUM: return 'blue';
      case ProjectPriority.HIGH: return 'orange';
      case ProjectPriority.URGENT: return 'red';
      default: return 'gray';
    }
  };

  // Table columns
  const columns = [
    {
      key: 'select',
      header: 'Select',
      render: (project: Project) => (
        <input
          type="checkbox"
          checked={selectedProjects.includes(project.id)}
          onChange={() => handleSelectProject(project.id)}
          className="rounded border-gray-600 bg-dark-700 text-primary-500 focus:ring-primary-500"
        />
      ),
    },
    {
      key: 'name',
      header: 'Project',
      render: (project: Project) => (
        <div className="flex items-center space-x-3">
          {project.mainImage ? (
            <img
              src={project.mainImage}
              alt={project.name}
              className="w-10 h-10 rounded-lg object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-500 to-accent-purple flex items-center justify-center">
              <FiFolder className="w-5 h-5 text-white" />
            </div>
          )}
          <div>
            <Link href={`/admin/projects/${project.id}`}>
              <h3 className="font-semibold text-white hover:text-primary-300 transition-colors">
                {project.name}
              </h3>
            </Link>
            <p className="text-sm text-primary-300 truncate max-w-xs">
              {project.description}
            </p>
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (project: Project) => (
        <Badge color={getStatusColor(project.status)} size="sm">
          {project.status.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      key: 'priority',
      header: 'Priority',
      render: (project: Project) => (
        <Badge color={getPriorityColor(project.priority)} size="sm">
          {project.priority}
        </Badge>
      ),
    },
    {
      key: 'progress',
      header: 'Progress',
      render: (project: Project) => (
        <div className="flex items-center space-x-2">
          <div className="w-16 bg-dark-600 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-primary-500 to-accent-purple h-2 rounded-full transition-all duration-300"
              style={{ width: `${project.progress}%` }}
            />
          </div>
          <span className="text-sm text-primary-300">{project.progress}%</span>
        </div>
      ),
    },
    {
      key: 'members',
      header: 'Team',
      render: (project: Project) => (
        <div className="flex items-center space-x-1">
          <FiUsers className="w-4 h-4 text-primary-300" />
          <span className="text-sm text-primary-300">{project.members.length}</span>
        </div>
      ),
    },
    {
      key: 'deadline',
      header: 'Deadline',
      render: (project: Project) => (
        project.deadline ? (
          <div className="flex items-center space-x-1">
            <FiCalendar className="w-4 h-4 text-primary-300" />
            <span className="text-sm text-primary-300">
              {new Date(project.deadline).toLocaleDateString()}
            </span>
          </div>
        ) : (
          <span className="text-sm text-gray-500">No deadline</span>
        )
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (project: Project) => (
        <div className="flex items-center space-x-2">
          <Link href={`/admin/projects/${project.id}`}>
            <Button variant="ghost" size="sm">
              <FiEye className="w-4 h-4" />
            </Button>
          </Link>
          <Link href={`/admin/projects/${project.id}/edit`}>
            <Button variant="ghost" size="sm">
              <FiEdit3 className="w-4 h-4" />
            </Button>
          </Link>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleStatus(project.id, project.isFeatured ? 'unfeature' : 'feature')}
            className={project.isFeatured ? 'text-yellow-400' : 'text-gray-400'}
          >
            <FiStar className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setProjectToDelete(project.id);
              setShowDeleteModal(true);
            }}
            className="text-red-400 hover:text-red-300"
          >
            <FiTrash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-7xl mx-auto">
          <Alert
            type="error"
            title="Error loading projects"
            message={error instanceof Error ? error.message : 'An unexpected error occurred'}
            actions={
              <Button onClick={handleRefresh} variant="primary" size="sm">
                Try Again
              </Button>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-br from-primary-500 to-accent-purple rounded-xl">
              <FiFolder className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Project Management</h1>
              <p className="text-primary-300 mt-1">
                Manage projects, track progress, and coordinate teams
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              onClick={handleRefresh}
              variant="secondary"
              size="sm"
              disabled={isLoading}
            >
              <FiRefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            <Link href="/admin/projects/create">
              <Button variant="primary" className="flex items-center space-x-2">
                <FiPlus className="w-4 h-4" />
                <span>New Project</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters and Search */}
        <GlassCard className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary-300 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap items-center space-x-4">
              <select
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                className="px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.header}
                  </option>
                ))}
              </select>

              <select
                value={filters.priority || ''}
                onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
                className="px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {priorityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.header}
                  </option>
                ))}
              </select>

              {/* View Mode Toggle */}
              <div className="flex items-center bg-dark-700 rounded-lg p-1">
                <Button
                  variant={viewMode === 'table' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                  className="px-3 py-1"
                >
                  <FiList className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="px-3 py-1"
                >
                  <FiGrid className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedProjects.length > 0 && (
            <div className="mt-4 flex items-center justify-between p-3 bg-dark-800 rounded-lg border border-primary-500/20">
              <span className="text-primary-300">
                {selectedProjects.length} project{selectedProjects.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => setShowBulkDeleteModal(true)}
                  disabled={bulkDeleteMutation.isPending}
                >
                  <FiTrash2 className="w-4 h-4" />
                  Delete Selected
                </Button>
              </div>
            </div>
          )}
        </GlassCard>

        {/* Projects Table/Grid */}
        <GlassCard className="overflow-hidden">
          {projects.length === 0 ? (
            <div className="text-center py-12">
              <FiFolder className="w-16 h-16 text-primary-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No projects found</h3>
              <p className="text-primary-300 mb-6">
                {filters.search || filters.status || filters.priority
                  ? 'Try adjusting your filters or search terms.'
                  : 'Get started by creating your first project.'}
              </p>
              <Link href="/admin/projects/create">
                <Button variant="primary">
                  <FiPlus className="w-4 h-4 mr-2" />
                  Create Project
                </Button>
              </Link>
            </div>
          ) : (
            <div>
              <div className="mb-4 flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedProjects.length === projects.length && projects.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-600 bg-dark-700 text-primary-500 focus:ring-primary-500"
                />
                <span className="text-sm text-primary-300">
                  Select All ({selectedProjects.length} selected)
                </span>
              </div>
              <DataTable
                columns={columns}
                data={projects}
                loading={isLoading}
                pagination={{
                  currentPage: filters.page || 1,
                  totalPages: Math.ceil(totalProjects / (filters.limit || 10)),
                  onPageChange: handlePageChange
                }}
              />
            </div>
          )}
        </GlassCard>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Delete Project"
          size="md"
        >
          <div className="space-y-4">
            <p className="text-primary-300">
              Are you sure you want to delete this project? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteProject}
                disabled={deleteProjectMutation.isPending}
              >
                {deleteProjectMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </Modal>

        {/* Bulk Delete Confirmation Modal */}
        <Modal
          isOpen={showBulkDeleteModal}
          onClose={() => setShowBulkDeleteModal(false)}
          title="Delete Projects"
          size="md"
        >
          <div className="space-y-4">
            <p className="text-primary-300">
              Are you sure you want to delete {selectedProjects.length} project{selectedProjects.length !== 1 ? 's' : ''}? 
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="secondary"
                onClick={() => setShowBulkDeleteModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleBulkDelete}
                disabled={bulkDeleteMutation.isPending}
              >
                {bulkDeleteMutation.isPending ? 'Deleting...' : 'Delete All'}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
}
