"use client";

import { useMutation } from '@tanstack/react-query';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

interface LoginCredentials {
  email: string;
  password: string;
  isAdmin?: boolean;
}

export const useLogin = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        isAdmin: credentials.isAdmin || false,
        redirect: false,
      });

      if (result?.error) {
        throw new Error('Invalid credentials');
      }

      return result;
    },
    onSuccess: (data, variables) => {
      toast.success('Login successful!');
      
      // Enhanced role-based redirection
      if (variables.isAdmin) {
        router.push('/admin/dashboard');
      } else {
        router.push('/profile');
      }
    },
    onError: (error: any) => {
      console.error('❌ Login Error:', error);

      // Extract error message from different possible error structures
      let errorMessage = '<PERSON><PERSON> failed. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Handle specific error cases
      if (error?.response?.status === 401) {
        errorMessage = 'Invalid email or password. Please check your credentials.';
      } else if (error?.response?.status === 429) {
        errorMessage = 'Too many login attempts. Please try again later.';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error?.isNetworkError) {
        errorMessage = 'Network error. Please check your connection.';
      }

      toast.error(errorMessage);
    },
  });
};
