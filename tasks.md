# Task Management System

## Overview
The task management system enables detailed task assignment and tracking for each project. Tasks are linked to specific employees, projects, and have clear statuses and deadlines.

## Features
- Admin can:
  - Create tasks per project.
  - Assign tasks to employees.
  - Define task status and deadlines.
  - Set project manager (optional per task).
  - View, edit, or delete tasks.

## Fields
- Title
- Description
- Status (`Pending`, `In Progress`, `Completed`, `Blocked`)
- Assigned To (Employee ref)
- Project Ref
- Start Date / Due Date
- Priority (optional)

## Integration with Other Systems
- **Project System**:
  - Tasks are attached to a project.
  - Project progress calculated via task statuses.
- **Employee Management**:
  - Each task is assigned to an employee.
  - <PERSON><PERSON> can filter tasks by employee.

## Admin Panel Features
- Timeline or Kanban board view (optional UI enhancement).
- Task filtering by project, employee, or status.
- Overview charts for project progress.

## Notes
- Only <PERSON><PERSON> can create/delete tasks.
- Tasks are essential to project progress tracking.
- Deleting a task updates associated project stats.

---

**End of Systems Documentation**

