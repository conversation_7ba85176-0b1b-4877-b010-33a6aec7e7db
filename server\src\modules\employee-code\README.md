# Employee Code System

This module provides a complete system for managing employee codes in NestJS with MongoDB (Mongoose).

## Features
- Create employee code with validation and unique constraint
- Mark code as used
- Check code validity (unused)
- List all unused codes
- Full validation using class-validator
- Enum role support: EMPLOYEE, ADMIN, TEAM_LEADER, PROJECT_MANAGER
- Prevent duplicate codes
- Timestamps for createdAt and updatedAt

## Endpoints
- `POST /codes` — Create a new code
- `GET /codes/check/:code` — Check code validity
- `PATCH /codes/use/:code` — Mark code as used
- `GET /codes/unused` — List unused codes

## Integration
- Designed for easy integration with auth-system and user registration
- Uses standard NestJS patterns (module, service, controller, DTOs, schema)

## Validation
- `code`: string, 4-10 chars, unique
- `description`: non-empty string
- `role`: one of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ADMIN, TEAM_LEADER, PROJECT_MANAGER

## Usage
Import `EmployeeCodeModule` in your app/module as needed.
