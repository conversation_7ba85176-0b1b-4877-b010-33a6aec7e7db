// Task Management Types
export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  IN_REVIEW = 'IN_REVIEW',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum TaskType {
  FEATURE = 'FEATURE',
  BUG = 'BUG',
  IMPROVEMENT = 'IMPROVEMENT',
  RESEARCH = 'RESEARCH',
  DOCUMENTATION = 'DOCUMENTATION',
  TESTING = 'TESTING',
  MAINTENANCE = 'MAINTENANCE'
}

export interface TaskComment {
  id: string;
  author: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  isEdited: boolean;
}

export interface TaskAttachment {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export interface TaskTimeLog {
  id: string;
  user: string;
  description: string;
  hoursSpent: number;
  logDate: Date;
  createdAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  projectId: string;
  assignedTo?: string;
  createdBy: string;
  reviewer?: string;
  dueDate?: Date;
  startDate?: Date;
  completedAt?: Date;
  estimatedHours?: number;
  actualHours?: number;
  progress: number;
  tags: string[];
  dependencies: string[];
  subtasks: string[];
  parentTask?: string;
  comments: TaskComment[];
  attachments: TaskAttachment[];
  timeLogs: TaskTimeLog[];
  technologies: string[];
  isBlocked: boolean;
  blockedReason?: string;
  isActive: boolean;
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTaskDto {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  projectId: string;
  assignedTo?: string;
  reviewer?: string;
  dueDate?: Date;
  startDate?: Date;
  estimatedHours?: number;
  tags?: string[];
  dependencies?: string[];
  parentTask?: string;
  technologies?: string[];
}

export interface UpdateTaskDto {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  assignedTo?: string;
  reviewer?: string;
  dueDate?: Date;
  startDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  progress?: number;
  tags?: string[];
  dependencies?: string[];
  technologies?: string[];
  isBlocked?: boolean;
  blockedReason?: string;
  isActive?: boolean;
}

export interface TaskFilters {
  search?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  type?: TaskType;
  projectId?: string;
  assignedTo?: string;
  createdBy?: string;
  reviewer?: string;
  dueDate?: Date;
  isBlocked?: boolean;
  page?: number;
  limit?: number;
}

export interface CreateCommentDto {
  taskId: string;
  content: string;
}

export interface CreateTimeLogDto {
  taskId: string;
  description: string;
  hoursSpent: number;
  logDate: Date;
}
