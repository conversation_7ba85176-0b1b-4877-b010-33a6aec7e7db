'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import axios from 'axios';
import { Card, CardHeader } from '@/components/shared/Card';
import { FormField, Input } from '@/components/shared/forms/FormField';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { Steps } from '@/components/shared/navigation/Breadcrumb';

const registrationSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters').max(50, 'Last name must be less than 50 characters'),
  email: z.string().email('Please enter a valid email address'),
  functionalCode: z.string().min(4, 'Employee code must be at least 4 characters').max(10, 'Employee code must be less than 10 characters'),
});

type RegistrationFormData = z.infer<typeof registrationSchema>;

export default function RegisterForm() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
  });

  const onSubmit = async (data: RegistrationFormData) => {
    setLoading(true);
    setError('');

    try {
      await axios.post('/api/auth/initiate-registration', data);

      // Store registration data for the next steps
      sessionStorage.setItem('registrationData', JSON.stringify(data));

      router.push(`/auth/register/verify-otp?email=${encodeURIComponent(data.email)}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    { title: 'Employee Details', description: 'Enter your information and employee code' },
    { title: 'Verify Email', description: 'Confirm your email with OTP' },
    { title: 'Set Password', description: 'Create your secure password' },
    { title: 'Complete', description: 'Registration successful' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <Steps
            current={0}
            items={steps}
            size="sm"
            direction="horizontal"
          />
        </div>

        <Card className="p-8">
          <CardHeader
            title="Register with Employee Code"
            subtitle="Step 1 of 4: Enter your details and employee code"
            className="text-center mb-6"
          />

          {error && (
            <Alert
              type="error"
              message={error}
              closable
              onClose={() => setError('')}
              className="mb-6"
            />
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <FormField
                label="First Name"
                required
                error={errors.firstName?.message}
              >
                <Input
                  {...register('firstName')}
                  placeholder="Enter your first name"
                  error={!!errors.firstName}
                  disabled={loading}
                />
              </FormField>

              <FormField
                label="Last Name"
                required
                error={errors.lastName?.message}
              >
                <Input
                  {...register('lastName')}
                  placeholder="Enter your last name"
                  error={!!errors.lastName}
                  disabled={loading}
                />
              </FormField>
            </div>

            <FormField
              label="Email Address"
              required
              error={errors.email?.message}
              description="We'll send a verification code to this email"
            >
              <Input
                {...register('email')}
                type="email"
                placeholder="Enter your email address"
                error={!!errors.email}
                disabled={loading}
              />
            </FormField>

            <FormField
              label="Employee Code"
              required
              error={errors.functionalCode?.message}
              description="Enter the employee code provided by your administrator"
            >
              <Input
                {...register('functionalCode')}
                placeholder="Enter your employee code"
                error={!!errors.functionalCode}
                disabled={loading}
                className="font-mono"
              />
            </FormField>

            <Button
              type="submit"
              fullWidth
              loading={loading}
              loadingText="Verifying employee code..."
              size="lg"
            >
              Continue to Email Verification
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Don't have an employee code?{' '}
              <a
                href="mailto:<EMAIL>"
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Contact your administrator
              </a>
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}
