# SillaLink Backend Environment Configuration
# Copy this file to .env and update the values

# Application Configuration
NODE_ENV=development
HOST=localhost
PORT=5000
NAME=SillaLink API
BASE_URL=http://localhost:5000
VERSION=v1

# Database Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27027
MONGODB_USERNAME=your_mongodb_username
MONGODB_PASSWORD=your_mongodb_password
MONGODB_NAME=silla_link

# JWT Configuration
JWT_ACCESS_SECRET=your-super-secret-access-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRED_ACCESS=15m
JWT_EXPIRED_REFRESH=7d
JWT_CHECK_EMAIL_EXPIRED=24h
REFRESH_TOKEN_REDIS_EXPIERD=604800

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6389
REDIS_PASSWORD=your_redis_password
REDIS_DATABASE_INDEX=0
REDIS_NAME=sillalink_redis
OTP_TIME=300

# Email Configuration (Required for validation)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password
MAIL_FROM_NAME=SillaLink

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=./logs

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production
