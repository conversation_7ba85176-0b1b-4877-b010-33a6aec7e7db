import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Task, TaskDocument } from '../schemas/task.schema';
import { TaskFilterDto } from '../../dto/task.dto';

@Injectable()
export class TaskRepository {
  constructor(
    @InjectModel(Task.name) private taskModel: Model<TaskDocument>,
  ) {}

  async create(taskData: any): Promise<TaskDocument> {
    const task = new this.taskModel(taskData);
    return await task.save();
  }

  async findAll(filters: TaskFilterDto = {}): Promise<TaskDocument[]> {
    const query: any = { isDeleted: false };

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }

    if (filters.priority) {
      query.priority = filters.priority;
    }

    if (filters.projectId) {
      query.project = new Types.ObjectId(filters.projectId);
    }

    if (filters.assignedTo) {
      query.assignedTo = new Types.ObjectId(filters.assignedTo);
    }

    if (filters.createdBy) {
      query.createdBy = new Types.ObjectId(filters.createdBy);
    }

    if (filters.technologies && filters.technologies.length > 0) {
      query.technologies = { $in: filters.technologies.map(id => new Types.ObjectId(id)) };
    }

    if (filters.dueDateFrom || filters.dueDateTo) {
      query.dueDate = {};
      if (filters.dueDateFrom) {
        query.dueDate.$gte = new Date(filters.dueDateFrom);
      }
      if (filters.dueDateTo) {
        query.dueDate.$lte = new Date(filters.dueDateTo);
      }
    }

    if (filters.search) {
      query.$or = [
        { title: { $regex: filters.search, $options: 'i' } },
        { description: { $regex: filters.search, $options: 'i' } },
      ];
    }

    return await this.taskModel
      .find(query)
      .populate('project', 'name description')
      .populate('assignedTo', 'name email role')
      .populate('createdBy', 'name email')
      .populate('technologies', 'name icon')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findById(id: string): Promise<TaskDocument | null> {
    return await this.taskModel
      .findOne({ _id: id, isDeleted: false })
      .populate('project', 'name description')
      .populate('assignedTo', 'name email role')
      .populate('createdBy', 'name email')
      .populate('technologies', 'name icon')
      .exec();
  }

  async findByProject(projectId: string): Promise<TaskDocument[]> {
    return await this.taskModel
      .find({ project: new Types.ObjectId(projectId), isDeleted: false })
      .populate('assignedTo', 'name email role')
      .populate('createdBy', 'name email')
      .populate('technologies', 'name icon')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findByAssignee(assigneeId: string): Promise<TaskDocument[]> {
    return await this.taskModel
      .find({ assignedTo: new Types.ObjectId(assigneeId), isDeleted: false })
      .populate('project', 'name description')
      .populate('createdBy', 'name email')
      .populate('technologies', 'name icon')
      .sort({ createdAt: -1 })
      .exec();
  }

  async update(id: string, updateData: any): Promise<TaskDocument> {
    return await this.taskModel
      .findOneAndUpdate(
        { _id: id, isDeleted: false },
        updateData,
        { new: true, runValidators: true }
      )
      .populate('project', 'name description')
      .populate('assignedTo', 'name email role')
      .populate('createdBy', 'name email')
      .populate('technologies', 'name icon')
      .exec();
  }

  async softDelete(id: string, deletedBy: string): Promise<void> {
    await this.taskModel
      .findByIdAndUpdate(id, {
        isDeleted: true,
        deletedBy: new Types.ObjectId(deletedBy),
        deletedAt: new Date(),
      })
      .exec();
  }

  async hardDelete(id: string): Promise<void> {
    await this.taskModel.findByIdAndDelete(id).exec();
  }

  async getStatistics(projectId?: string): Promise<any> {
    const matchStage: any = { isDeleted: false };
    if (projectId) {
      matchStage.projectId = new Types.ObjectId(projectId);
    }

    const stats = await this.taskModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          todo: { $sum: { $cond: [{ $eq: ['$status', 'TODO'] }, 1, 0] } },
          inProgress: { $sum: { $cond: [{ $eq: ['$status', 'IN_PROGRESS'] }, 1, 0] } },
          inReview: { $sum: { $cond: [{ $eq: ['$status', 'IN_REVIEW'] }, 1, 0] } },
          completed: { $sum: { $cond: [{ $eq: ['$status', 'COMPLETED'] }, 1, 0] } },
          cancelled: { $sum: { $cond: [{ $eq: ['$status', 'CANCELLED'] }, 1, 0] } },
          highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'HIGH'] }, 1, 0] } },
          mediumPriority: { $sum: { $cond: [{ $eq: ['$priority', 'MEDIUM'] }, 1, 0] } },
          lowPriority: { $sum: { $cond: [{ $eq: ['$priority', 'LOW'] }, 1, 0] } },
          overdue: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$status', 'COMPLETED'] },
                    { $ne: ['$status', 'CANCELLED'] },
                    { $lt: ['$dueDate', new Date()] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    return stats[0] || {
      total: 0,
      todo: 0,
      inProgress: 0,
      inReview: 0,
      completed: 0,
      cancelled: 0,
      highPriority: 0,
      mediumPriority: 0,
      lowPriority: 0,
      overdue: 0
    };
  }

  async getTasksByStatus(): Promise<any> {
    return await this.taskModel.aggregate([
      { $match: { isDeleted: false } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          tasks: { $push: '$$ROOT' }
        }
      }
    ]);
  }

  async getTasksByPriority(): Promise<any> {
    return await this.taskModel.aggregate([
      { $match: { isDeleted: false } },
      {
        $group: {
          _id: '$priority',
          count: { $sum: 1 },
          tasks: { $push: '$$ROOT' }
        }
      }
    ]);
  }

  async getOverdueTasks(): Promise<TaskDocument[]> {
    return await this.taskModel
      .find({
        isDeleted: false,
        status: { $nin: ['COMPLETED', 'CANCELLED'] },
        dueDate: { $lt: new Date() }
      })
      .populate('projectId', 'name description')
      .populate('assignedTo', 'name email role')
      .populate('createdBy', 'name email')
      .sort({ dueDate: 1 })
      .exec();
  }
}
