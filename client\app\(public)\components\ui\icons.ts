// Using static paths instead of imports to avoid SVG loader issues
const MuiIcon = '/assets/MUIicon.svg';
const GoIcon = '/assets/GoIcon.svg';
const figmaIcon = '/assets/figmaIcon.svg';
const ReactIcon = '/assets/ReactIcon.svg';
const CssIcon = '/assets/CssIcon.svg';
const NodeIcon = '/assets/NodeIcon.svg';
const JavaScriptIcon = '/assets/JavaScriptIcon.svg';
const MongoIcon = '/assets/MongoIcon.svg';
const TypeScriptIcon = '/assets/TypeScriptIcon.svg';
const NextIcon = '/assets/NextIcon.svg';
const TailwindIcon = '/assets/tailwindIcon.svg';
const FlutterIcon = '/assets/FlutterIcon.svg';
const HtmlIcon = '/assets/HtmlIcon.svg';
const ViteIcon = '/assets/ViteIcon.svg';

export const icons = [
  {
    icon: MuiIcon,
    alt: "MUIIcon"
  },
  {
    icon: GoIcon,
    alt: "Go icon"
  },
  {
    icon: figmaIcon,
    alt: "Figma icon"
  },
  {
    icon: ReactIcon,
    alt: "React icon"
  },
  {
    icon: CssIcon,
    alt: "Css icon"
  },
  {
    icon: NodeIcon,
    alt: "Node icon"
  },
  {
    icon: MongoIcon,
    alt: "Mongo Icon"
  },
  {
    icon: HtmlIcon,
    alt: "Html icon"
  },
  {
    icon: TypeScriptIcon,
    alt: "Type script icon"
  },
  {
    icon: NextIcon,
    alt: "next icon"
  },
  {
    icon: FlutterIcon,
    alt: "Flutter icon"
  },
  {
    icon: TailwindIcon,
    alt: "Tailwind icon"
  },
  {
    icon: ViteIcon,
    alt: "Vite icon"
  },
  {
    icon: JavaScriptIcon,
    alt: "javaScript icon"
  },
]
