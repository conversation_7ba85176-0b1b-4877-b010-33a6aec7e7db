"use client";

import { useMutation } from '@tanstack/react-query';
import { authApi } from '@/lib/api/auth';
import { toast } from 'react-hot-toast';

export interface InitiateRegistrationPayload {
  firstName: string;
  lastName: string;
  email: string;
  functionalCode: string;
  phone?: string;
}

export const useInitiateRegistration = () => {
  return useMutation({
    mutationFn: async (data: InitiateRegistrationPayload) => {
      const response = await authApi.register(data);
      return response;
    },
    onError: (error: any) => {
      console.error('❌ Registration Error:', error);

      // Extract error message from different possible error structures
      let errorMessage = 'Registration failed. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Handle specific error cases
      if (error?.response?.status === 400) {
        if (error.response.data?.message?.includes('Employee code')) {
          errorMessage = 'Invalid or expired employee code. Please check with your administrator.';
        } else if (error.response.data?.message?.includes('email')) {
          errorMessage = 'This email address is already registered.';
        }
      } else if (error?.response?.status === 429) {
        errorMessage = 'Too many registration attempts. Please try again later.';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error?.isNetworkError) {
        errorMessage = 'Network error. Please check your connection.';
      }

      toast.error(errorMessage);
    },
  });
};

export interface VerifyOtpPayload {
  email: string;
  otp: string;
}

export const useVerifyOtp = () => {
  return useMutation({
    mutationFn: async (data: VerifyOtpPayload) => {
      const response = await authApi.verifyOtp(data);
      return response;
    },
    onError: (error: any) => {
      console.error('❌ OTP Verification Error:', error);

      // Extract error message from different possible error structures
      let errorMessage = 'OTP verification failed. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Handle specific error cases
      if (error?.response?.status === 400) {
        if (error.response.data?.message?.includes('OTP')) {
          errorMessage = 'Invalid or expired OTP. Please check the code or request a new one.';
        }
      } else if (error?.response?.status === 429) {
        errorMessage = 'Too many OTP attempts. Please try again later.';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error?.isNetworkError) {
        errorMessage = 'Network error. Please check your connection.';
      }

      toast.error(errorMessage);
    },
  });
};

export interface SetPasswordPayload {
  email: string;
  password: string;
}

export const useSetPassword = () => {
  return useMutation({
    mutationFn: async (data: SetPasswordPayload) => {
      const response = await authApi.setPassword(data);
      return response;
    },
    onError: (error: any) => {
      console.error('❌ Set Password Error:', error);

      // Extract error message from different possible error structures
      let errorMessage = 'Password setup failed. Please try again.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Handle specific error cases
      if (error?.response?.status === 400) {
        if (error.response.data?.message?.includes('password')) {
          errorMessage = 'Password does not meet requirements. Please choose a stronger password.';
        } else if (error.response.data?.message?.includes('User validation failed')) {
          errorMessage = 'Registration data expired. Please start the registration process again.';
        }
      } else if (error?.response?.status === 429) {
        errorMessage = 'Too many attempts. Please try again later.';
      } else if (error?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error?.isNetworkError) {
        errorMessage = 'Network error. Please check your connection.';
      }

      toast.error(errorMessage);
    },
  });
};
