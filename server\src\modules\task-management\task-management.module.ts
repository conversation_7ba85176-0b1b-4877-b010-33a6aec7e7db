import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { join, normalize } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { TaskAdminController } from './api/controllers/task.admin.controller';
import { TaskController } from './api/controllers/task.controller';
import { TaskService } from './services/task.service';
import { TaskRepository } from './database/repositories/task.repository';
import { TaskError } from './services/task.error';
import { Task, TaskSchema } from './database/schemas/task.schema';
import { UserManagementModule } from '../user-management/user-management.module';
import { ProjectManagementModule } from '../project-management/project-management.module';
import { TechnologyManagementModule } from '../technology-management/technology-management.module';

// File upload configuration for task attachments
const ALLOWED_FILE_TYPES = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar'];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

const taskMulterConfig = {
  storage: diskStorage({
    destination: (req, file, cb) => {
      const uploadPath = normalize(join(process.cwd(), 'uploads', 'tasks', 'attachments'));
      
      if (!existsSync(uploadPath)) {
        mkdirSync(uploadPath, { recursive: true });
      }
      
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const extension = file.originalname.split('.').pop();
      cb(null, `task-attachment-${uniqueSuffix}.${extension}`);
    },
  }),
  limits: {
    fileSize: MAX_FILE_SIZE,
  },
  fileFilter: (req, file, cb) => {
    try {
      const extension = file.originalname.split('.').pop()?.toLowerCase();
      const isValidType = ALLOWED_FILE_TYPES.includes(extension || '');

      if (!isValidType) {
        return cb(new Error(`Only ${ALLOWED_FILE_TYPES.join(', ')} files are allowed!`), false);
      }

      cb(null, true);
    } catch (error) {
      cb(error, false);
    }
  },
};

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Task.name, schema: TaskSchema }]),
    MulterModule.register(taskMulterConfig),
    UserManagementModule,
    ProjectManagementModule,
    TechnologyManagementModule
  ],
  controllers: [TaskAdminController, TaskController],
  providers: [TaskService, TaskRepository, TaskError],
  exports: [TaskService, TaskRepository]
})
export class TaskManagementModule {}
