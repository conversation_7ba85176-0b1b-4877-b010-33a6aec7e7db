/**
 * Comprehensive error handling utilities for preventing Next.js global errors
 * and providing user-friendly error messages with toast notifications
 */

import toast from 'react-hot-toast';

export interface ApiError {
  response?: {
    data?: {
      message?: string | string[];
      error?: string | {
        message?: string;
        code?: number;
        path?: string;
        time?: string;
        keyValue?: Record<string, any>;
        keyPattern?: Record<string, any>;
      };
      code?: number;
      keyValue?: Record<string, any>;
      keyPattern?: Record<string, any>;
      statusCode?: number;
      success?: boolean;
    };
    status?: number;
  };
  message?: string;
  code?: number;
  name?: string;
  isAxiosError?: boolean;
  isApiError?: boolean;
  isNetworkError?: boolean;
  isUnknownError?: boolean;
  status?: number;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  fallbackMessage?: string;
  onError?: (error: ApiError) => void;
  preventGlobalError?: boolean;
}

/**
 * Centralized error handler class that prevents Next.js global errors
 * and provides consistent error handling across the application
 */
export class ErrorHandler {
  /**
   * Extract a user-friendly error message from various error structures
   */
  static extractErrorMessage(error: any): string {
    // Handle null/undefined errors
    if (!error) {
      return 'An unexpected error occurred';
    }

    // Handle string errors
    if (typeof error === 'string') {
      return error;
    }

    // Handle API response errors (Axios structure)
    if (error?.response?.data) {
      const data = error.response.data;

      // Handle backend error structure
      if (data.error?.message) {
        return data.error.message;
      }

      // Handle direct message in data
      if (data.message) {
        return data.message;
      }

      // Handle error field in data
      if (data.error && typeof data.error === 'string') {
        return data.error;
      }
    }

    // Handle direct error message
    if (error?.message) {
      return error.message;
    }

    // Handle error with error property
    if (error?.error) {
      return typeof error.error === 'string' ? error.error : error.error.message || 'Unknown error';
    }

    return 'An unexpected error occurred';
  }

  /**
   * Get contextual error message based on status code and content
   */
  static getContextualErrorMessage(error: any): string {
    const baseMessage = this.extractErrorMessage(error);
    const status = error?.response?.status || error?.status;

    // Handle specific HTTP status codes
    switch (status) {
      case 400:
        if (baseMessage.toLowerCase().includes('validation')) {
          return baseMessage;
        }
        if (baseMessage.toLowerCase().includes('employee code')) {
          return 'Invalid or expired employee code. Please check with your administrator.';
        }
        if (baseMessage.toLowerCase().includes('email')) {
          return 'This email address is already registered or invalid.';
        }
        if (baseMessage.toLowerCase().includes('otp')) {
          return 'Invalid or expired verification code. Please try again.';
        }
        return baseMessage || 'Invalid request. Please check your input.';

      case 401:
        if (baseMessage.toLowerCase().includes('credentials')) {
          return 'Invalid email or password. Please check your credentials.';
        }
        return 'Authentication failed. Please log in again.';

      case 403:
        return 'You do not have permission to perform this action.';

      case 404:
        if (baseMessage.toLowerCase().includes('user')) {
          return 'Account not found. Please check your email address.';
        }
        return 'The requested resource was not found.';

      case 409:
        return 'This resource already exists. Please try with different information.';

      case 429:
        return 'Too many requests. Please wait a moment and try again.';

      case 500:
      case 502:
      case 503:
      case 504:
        return 'Server error. Please try again later.';

      default:
        if (error?.isNetworkError || error?.code === 'NETWORK_ERROR') {
          return 'Network error. Please check your internet connection.';
        }
        return baseMessage || 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Handle errors with toast notifications and prevent global errors
   */
  static handle(error: any, options: ErrorHandlerOptions = {}): void {
    const {
      showToast = true,
      fallbackMessage = 'An unexpected error occurred',
      onError,
      preventGlobalError = true
    } = options;

    console.error('🔴 Error Handler:', error);

    try {
      const errorMessage = this.getContextualErrorMessage(error) || fallbackMessage;

      // Show toast notification if enabled
      if (showToast) {
        toast.error(errorMessage);
      }

      // Call custom error handler if provided
      if (onError) {
        const apiError: ApiError = {
          message: errorMessage,
          status: error?.response?.status || error?.status,
          code: error?.response?.data?.code || error?.code,
          response: error?.response,
          isApiError: !!error?.response,
          isNetworkError: error?.isNetworkError || error?.code === 'NETWORK_ERROR',
          isUnknownError: !error?.response && !error?.isNetworkError
        };

        onError(apiError);
      }

      // Prevent Next.js global error by not re-throwing
      if (preventGlobalError) {
        return;
      }
    } catch (handlerError) {
      // Fallback if error handler itself fails
      console.error('🔴 Error Handler failed:', handlerError);
      if (showToast) {
        toast.error(fallbackMessage);
      }
    }
  }

  /**
   * Async wrapper that catches and handles errors from promises
   */
  static async handleAsync<T>(
    promise: Promise<T>,
    options: ErrorHandlerOptions = {}
  ): Promise<T | null> {
    try {
      return await promise;
    } catch (error) {
      this.handle(error, options);
      return null;
    }
  }
}

/**
 * Legacy function for backward compatibility
 * Enhanced to handle nested error structures like response.data.error
 */
export const extractErrorMessage = (error: ApiError, defaultMessage: string = 'An error occurred'): string => {
  console.group('🔍 Enhanced Error Handler - Processing Error');
  console.log('Error Type:', typeof error);
  console.log('Is Axios Error:', error?.isAxiosError);
  console.log('Error Name:', error?.name);
  console.log('Error Message:', error?.message);
  console.log('Response Status:', error?.response?.status);
  console.log('Response Data:', error?.response?.data);

  if (error?.response?.data?.error) {
    console.log('Nested Error Object:', error.response.data.error);
  }
  console.groupEnd();

  // Handle different error response structures
  if (error?.response?.data) {
    const errorData = error.response.data;

    // 1. Handle nested error object in response.data.error (NEW - highest priority)
    if (typeof errorData.error === 'object' && errorData.error !== null) {
      const nestedError = errorData.error;

      // Handle MongoDB duplicate key errors in nested structure
      if (nestedError.code === 11000) {
        console.log('🔑 Nested Duplicate Key Error Detected');
        return handleDuplicateKeyError(nestedError);
      }

      // Handle nested error message
      if (nestedError.message) {
        console.log('📝 Nested Error Message:', nestedError.message);
        return nestedError.message;
      }
    }

    // 2. Handle MongoDB duplicate key errors at root level
    if (errorData.code === 11000) {
      console.log('🔑 Root Level Duplicate Key Error Detected');
      return handleDuplicateKeyError(errorData);
    }

    // 3. Handle validation errors (array of messages)
    if (Array.isArray(errorData.message)) {
      console.log('✅ Validation Errors Array:', errorData.message);
      return formatValidationErrors(errorData.message);
    }

    // 4. Handle single error message
    if (errorData.message && typeof errorData.message === 'string') {
      console.log('📝 Single Error Message:', errorData.message);
      return errorData.message;
    }

    // 5. Handle string error in errorData.error
    if (typeof errorData.error === 'string') {
      console.log('📝 String Error:', errorData.error);
      return errorData.error;
    }
  }

  // Handle AxiosError with status but no data
  if (error?.response?.status) {
    const status = error.response.status;
    switch (status) {
      case 400:
        return 'Bad request - please check your input';
      case 401:
        return 'Unauthorized - please log in again';
      case 403:
        return 'Forbidden - you do not have permission';
      case 404:
        return 'Not found';
      case 500:
        return 'Server error - please try again later';
      default:
        return `Request failed with status ${status}`;
    }
  }

  // Handle network errors
  if (error?.message) {
    return error.message;
  }

  return defaultMessage;
};

/**
 * Enhanced handler for MongoDB duplicate key errors
 * Supports both root level and nested error structures
 */
const handleDuplicateKeyError = (errorData: any): string => {
  console.log('🔑 Processing Duplicate Key Error:', errorData);

  // Try to extract keyValue from various possible locations
  const keyValue = errorData.keyValue || errorData.error?.keyValue;
  const keyPattern = errorData.keyPattern || errorData.error?.keyPattern;

  console.log('Key Value:', keyValue);
  console.log('Key Pattern:', keyPattern);

  if (!keyValue || Object.keys(keyValue).length === 0) {
    console.log('⚠️ No keyValue found, using generic message');
    return 'A record with this information already exists';
  }

  // Get the first field that caused the duplicate error
  const duplicateField = Object.keys(keyValue)[0];
  const duplicateValue = duplicateField ? keyValue[duplicateField] : undefined;

  console.log(`🎯 Duplicate field: ${duplicateField}, value: ${duplicateValue}`);

  // Handle specific duplicate key scenarios with user-friendly messages
  switch (duplicateField) {
    case 'nationalId':
      return `An employee with National ID "${duplicateValue}" already exists`;

    case 'email':
      return `An employee with email "${duplicateValue}" already exists`;

    case 'employeeId':
      return `An employee with ID "${duplicateValue}" already exists`;

    case 'phone':
      return `An employee with phone number "${duplicateValue}" already exists`;

    case 'username':
      return `Username "${duplicateValue}" is already taken`;

    case 'code':
      return `Code "${duplicateValue}" is already in use`;

    case 'name':
      return `Name "${duplicateValue}" already exists`;

    default:
      // Generic message for unknown fields
      return `A record with ${duplicateField} "${duplicateValue}" already exists`;
  }
};

/**
 * Handles validation errors and formats them nicely
 */
export const formatValidationErrors = (errors: string[]): string => {
  if (errors.length === 1) {
    return errors[0] || 'Validation error occurred';
  }

  return `Multiple validation errors: ${errors.join(', ')}`;
};

/**
 * Checks if an error is a network error
 */
export const isNetworkError = (error: ApiError): boolean => {
  return !error?.response && !!error?.message;
};

/**
 * Checks if an error is a validation error
 */
export const isValidationError = (error: ApiError): boolean => {
  return error?.response?.status === 400 || error?.response?.data?.statusCode === 400;
};

/**
 * Checks if an error is an authorization error
 */
export const isAuthError = (error: ApiError): boolean => {
  return error?.response?.status === 401 || error?.response?.status === 403;
};

/**
 * Enhanced check for duplicate key errors
 * Handles both root level and nested error structures
 */
export const isDuplicateKeyError = (error: ApiError): boolean => {
  const errorData = error?.response?.data;
  if (!errorData) return false;

  // Check root level code
  if (errorData.code === 11000) return true;

  // Check nested error code
  if (typeof errorData.error === 'object' && errorData.error?.code === 11000) return true;

  return false;
};

/**
 * Gets the HTTP status code from an error
 */
export const getErrorStatusCode = (error: ApiError): number | undefined => {
  return error?.response?.status || error?.response?.data?.statusCode;
};

/**
 * Extracts duplicate field information from error
 */
export const extractDuplicateFieldInfo = (error: ApiError): { field: string; value: any } | null => {
  if (!isDuplicateKeyError(error)) return null;

  const errorData = error?.response?.data;
  if (!errorData) return null;

  // Try to extract keyValue from various locations
  const keyValue = errorData.keyValue ||
                   (typeof errorData.error === 'object' ? errorData.error?.keyValue : null);

  if (keyValue && Object.keys(keyValue).length > 0) {
    const field = Object.keys(keyValue)[0];
    const value = field ? keyValue[field] : undefined;
    return { field: field || '', value };
  }

  return null;
};

/**
 * Gets user-friendly field name for error messages
 */
export const getFieldDisplayName = (fieldName: string): string => {
  const fieldMap: Record<string, string> = {
    nationalId: 'National ID',
    email: 'Email',
    phone: 'Phone Number',
    employeeId: 'Employee ID',
    username: 'Username',
    firstName: 'First Name',
    lastName: 'Last Name',
    position: 'Position',
    department: 'Department'
  };

  return fieldMap[fieldName] || fieldName;
};

/**
 * Creates a standardized error object for logging
 */
export const createErrorLog = (error: ApiError, context: string) => {
  const duplicateInfo = extractDuplicateFieldInfo(error);

  return {
    context,
    message: extractErrorMessage(error),
    statusCode: getErrorStatusCode(error),
    isNetworkError: isNetworkError(error),
    isValidationError: isValidationError(error),
    isAuthError: isAuthError(error),
    isDuplicateKey: isDuplicateKeyError(error),
    duplicateField: duplicateInfo?.field,
    duplicateValue: duplicateInfo?.value,
    timestamp: new Date().toISOString(),
    originalError: error
  };
};

/**
 * Hook-friendly error handler for React components
 */
export const useErrorHandler = () => {
  const handleError = (error: any, options?: ErrorHandlerOptions) => {
    ErrorHandler.handle(error, options);
  };

  const handleFormError = (error: any, setError?: (message: string) => void) => {
    const errorMessage = ErrorHandler.getContextualErrorMessage(error);

    // Set form error if setter is provided
    if (setError) {
      setError(errorMessage);
    }

    // Also show toast for immediate feedback
    toast.error(errorMessage);
  };

  const handleAsync = async <T>(
    promise: Promise<T>,
    options?: ErrorHandlerOptions
  ): Promise<T | null> => {
    return ErrorHandler.handleAsync(promise, options);
  };

  return {
    handleError,
    handleFormError,
    handleAsync,
    extractMessage: ErrorHandler.extractErrorMessage,
    getContextualMessage: ErrorHandler.getContextualErrorMessage
  };
};

/**
 * User's specific HandleError function for backend validation errors
 * Handles nested error structures and validation arrays
 */
export const HandleError = (error: any): string => {
  // Handle validation errors from backend (array of messages)
  if (error.response && error.response.data && error.response.data.message && Array.isArray(error.response.data.message)) {
    return error.response.data.message.join(', ');
  }

  // Handle single error message from backend
  if (error.response && error.response.data && error.response.data.message) {
    return error.response.data.message;
  }

  // Handle errors array structure (for specific backend validation)
  if (error.response && error.response.data && error.response.data.errors && Array.isArray(error.response.data.errors) && error.response.data.errors[0]?.msg) {
    return error.response.data.errors[0].msg;
  }

  // Handle direct error message
  if (error.message) {
    return error.message;
  }

  // Fallback to string conversion
  return error.toString();
};

export default HandleError;
