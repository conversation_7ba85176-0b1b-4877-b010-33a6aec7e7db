// Project Management Types
export enum ProjectStatus {
  PLANNING = 'PLANNING',
  IN_PROGRESS = 'IN_PROGRESS',
  ON_HOLD = 'ON_HOLD',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ARCHIVED = 'ARCHIVED'
}

export enum ProjectPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum ProjectMemberRole {
  MEMBER = 'MEMBER',
  LEAD = 'LEAD',
  CONTRIBUTOR = 'CONTRIBUTOR',
  REVIEWER = 'REVIEWER'
}

export interface ProjectMember {
  userId: string;
  role: ProjectMemberRole;
  joinedAt: Date;
  responsibilities?: string[];
  hourlyRate?: number;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  longDescription?: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  startDate?: Date;
  endDate?: Date;
  deadline?: Date;
  progress: number;
  budget?: number;
  currency: string;
  projectManager: string;
  members: ProjectMember[];
  technologies: string[];
  tags: string[];
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  mainImage?: string;
  images: string[];
  repositoryUrl?: string;
  liveUrl?: string;
  demoUrl?: string;
  isActive: boolean;
  isFeatured: boolean;
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProjectDto {
  name: string;
  description?: string;
  longDescription?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  startDate?: Date;
  endDate?: Date;
  deadline?: Date;
  budget?: number;
  currency?: string;
  projectManager: string;
  members?: ProjectMember[];
  technologies?: string[];
  tags?: string[];
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  repositoryUrl?: string;
  liveUrl?: string;
  demoUrl?: string;
}

export interface UpdateProjectDto {
  name?: string;
  description?: string;
  longDescription?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  startDate?: Date;
  endDate?: Date;
  deadline?: Date;
  progress?: number;
  budget?: number;
  currency?: string;
  projectManager?: string;
  members?: ProjectMember[];
  technologies?: string[];
  tags?: string[];
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  repositoryUrl?: string;
  liveUrl?: string;
  demoUrl?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface ProjectFilters {
  search?: string;
  status?: ProjectStatus;
  priority?: ProjectPriority;
  projectManager?: string;
  technology?: string;
  startDate?: Date;
  endDate?: Date;
  isActive?: boolean;
  isFeatured?: boolean;
  page?: number;
  limit?: number;
}
