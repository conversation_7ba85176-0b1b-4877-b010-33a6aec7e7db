'use client';

import React from 'react';
import { cn } from '@/lib/utils/cn';
import { Spinner } from '@/components/shared/Spinner';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const buttonVariants = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600',
  secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 dark:bg-gray-500 dark:hover:bg-gray-600',
  outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700',
  ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600',
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 dark:bg-green-500 dark:hover:bg-green-600',
};

const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-sm',
  lg: 'px-6 py-3 text-base',
  xl: 'px-8 py-4 text-lg',
};

export const Button: React.FC<ButtonProps> = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText,
  leftIcon,
  rightIcon,
  fullWidth = false,
  disabled,
  ...props
}) => {
  const isDisabled = disabled || loading;

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        buttonVariants[variant],
        buttonSizes[size],
        fullWidth && 'w-full',
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {loading && (
        <Spinner className="mr-2 h-4 w-4" />
      )}
      {!loading && leftIcon && (
        <span className="mr-2">{leftIcon}</span>
      )}
      {loading && loadingText ? loadingText : children}
      {!loading && rightIcon && (
        <span className="ml-2">{rightIcon}</span>
      )}
    </button>
  );
};

interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon: React.ReactNode;
  tooltip?: string;
}

const iconButtonSizes = {
  sm: 'p-1.5',
  md: 'p-2',
  lg: 'p-3',
};

export const IconButton: React.FC<IconButtonProps> = ({
  className,
  variant = 'ghost',
  size = 'md',
  loading = false,
  icon,
  tooltip,
  disabled,
  ...props
}) => {
  const isDisabled = disabled || loading;

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md transition-colors',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        buttonVariants[variant],
        iconButtonSizes[size],
        className
      )}
      disabled={isDisabled}
      title={tooltip}
      {...props}
    >
      {loading ? (
        <Spinner className="h-4 w-4" />
      ) : (
        icon
      )}
    </button>
  );
};

interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
}) => {
  return (
    <div
      className={cn(
        'inline-flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>button:not(:first-child)]:ml-0',
        '[&>button:not(:last-child)]:rounded-r-none',
        '[&>button:not(:first-child)]:rounded-l-none',
        '[&>button:not(:first-child)]:border-l-0',
        orientation === 'vertical' && [
          'flex-col',
          '[&>button:not(:first-child)]:ml-0',
          '[&>button:not(:first-child)]:mt-0',
          '[&>button:not(:last-child)]:rounded-b-none',
          '[&>button:not(:first-child)]:rounded-t-none',
          '[&>button:not(:first-child)]:border-t-0',
          '[&>button:not(:first-child)]:border-l',
        ],
        className
      )}
    >
      {children}
    </div>
  );
};

interface LoadingButtonProps extends ButtonProps {
  loadingText?: string;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  loading,
  loadingText,
  ...props
}) => {
  return (
    <Button loading={loading || false} {...props}>
      {loading && loadingText ? loadingText : children}
    </Button>
  );
};

interface ConfirmButtonProps extends ButtonProps {
  confirmText?: string;
  onConfirm?: () => void;
  requireConfirmation?: boolean;
}

export const ConfirmButton: React.FC<ConfirmButtonProps> = ({
  children,
  confirmText = 'Are you sure?',
  onConfirm,
  requireConfirmation = true,
  onClick,
  ...props
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (requireConfirmation) {
      if (window.confirm(confirmText)) {
        onConfirm?.();
        onClick?.(e);
      }
    } else {
      onConfirm?.();
      onClick?.(e);
    }
  };

  return (
    <Button onClick={handleClick} {...props}>
      {children}
    </Button>
  );
};

// Export all button components
export {
  buttonVariants,
  buttonSizes,
  iconButtonSizes,
};
