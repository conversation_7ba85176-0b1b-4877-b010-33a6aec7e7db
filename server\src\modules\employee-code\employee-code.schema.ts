import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum EmployeeCodeRole {
  EMPLOYEE = 'EMPLOYEE',
  ADMIN = 'ADMIN',
  TEAM_LEADER = 'TEAM_LEADER',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
}

export type EmployeeCodeDocument = EmployeeCode & Document;

@Schema({ timestamps: true, collection: 'employeeCodes' })
export class EmployeeCode {
  @Prop({ required: true, unique: true, minlength: 4, maxlength: 10, trim: true, uppercase: true })
  code: string;

  @Prop({ required: true, trim: true, maxlength: 500 })
  description: string;

  @Prop({ required: true, enum: EmployeeCodeRole })
  role: EmployeeCodeRole;

  @Prop({ default: false })
  isUsed: boolean;

  @Prop({ type: Types.ObjectId, ref: 'User', default: null })
  usedBy?: Types.ObjectId;

  @Prop({ type: Date, default: null })
  usedAt?: Date;

  @Prop({ type: Types.ObjectId, ref: 'User', required: false })
  createdBy?: Types.ObjectId;

  @Prop({ type: Date, default: null })
  expiresAt?: Date;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: String, trim: true, maxlength: 1000 })
  notes?: string;
}

export const EmployeeCodeSchema = SchemaFactory.createForClass(EmployeeCode);

// Indexes for better performance
EmployeeCodeSchema.index({ code: 1 }, { unique: true });
EmployeeCodeSchema.index({ role: 1, isUsed: 1 });
EmployeeCodeSchema.index({ isActive: 1, expiresAt: 1 });
EmployeeCodeSchema.index({ createdBy: 1 });
