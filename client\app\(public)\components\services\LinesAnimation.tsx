import { motion } from 'framer-motion';


interface AnimatedPathProps {
    d: string;
    delay?: number;
  }
  
  const AnimatedPath = ({ d, delay = 0 }: AnimatedPathProps) => (
    <motion.path
      d={d}
      fill="none"
      initial={{ pathLength: 0 }}
      whileInView={{ pathLength: 1 }}
      viewport={{ once: true }}
      transition={{
        duration: 1.5,
        delay,
        ease: "easeInOut",
      }}
      stroke="#6B2C91"
      strokeWidth="2"
    />
  );
  

export const Line1 = () => (
    <svg
      width="286"
      height="213"
      viewBox="0 0 286 213"
      className="w-full h-full object-contain"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <AnimatedPath
        d="M167.52 196.372C166.748 197.162 166.76 198.43 167.547 199.204L180.379 211.825C181.167 212.599 182.431 212.587 183.204 211.798C183.977 211.009 183.965 209.741 183.178 208.966L171.771 197.748L182.965 186.313C183.737 185.524 183.726 184.256 182.938 183.482C182.151 182.707 180.886 182.719 180.113 183.509L167.52 196.372ZM1.12148 9.94723C94.2332 -0.566403 160.417 5.95927 204.992 21.5329C249.555 37.1026 272.315 61.6215 279.15 87.0522C285.991 112.503 277.04 139.435 256.995 160.195C236.96 180.946 205.914 195.419 168.927 195.772L168.965 199.777C206.97 199.414 239.052 184.537 259.866 162.98C280.67 141.433 290.29 113.101 283.008 86.01C275.721 58.8992 251.666 33.5991 206.307 17.7514C160.959 1.90748 94.1141 -4.58293 0.674645 5.96771L1.12148 9.94723Z"
      />
    </svg>
  );
  
  export const Line2 = () => (
    <svg
      width="211"
      height="239"
      viewBox="0 0 211 239"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <AnimatedPath
        d="M108.098 228.46C108.53 229.475 108.056 230.651 107.04 231.086L90.4751 238.167C89.4586 238.602 88.2842 238.131 87.852 237.116C87.4198 236.1 87.8935 234.925 88.91 234.49L103.634 228.195L97.3739 213.485C96.9417 212.47 97.4154 211.294 98.4319 210.86C99.4484 210.425 100.623 210.896 101.055 211.911L108.098 228.46ZM210.751 4.24669C164.381 4.24672 122.347 13.3853 88.4965 28.2956C54.6195 43.2177 29.0728 63.8582 15.4759 86.7649C1.93144 109.583 0.196194 134.692 13.9368 159.042C27.7475 183.516 57.3372 207.473 107.005 227.39L105.509 231.103C55.4271 211.021 24.8917 186.603 10.4522 161.014C-4.05738 135.302 -2.1678 108.661 12.0375 84.7291C26.1905 60.8857 52.5186 39.776 86.8913 24.6355C121.291 9.48326 163.881 0.24668 210.761 0.246648L210.751 4.24669Z"
        delay={0.2}
      />
    </svg>
  );