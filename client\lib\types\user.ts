// User Management Types
export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN',
  EMPLOYEE = 'EMPLOYEE',
  TEAM_LEADER = 'TEAM_LEADER',
  PROJECT_MANAGER = 'PROJECT_MANAGER'
}

export interface RefreshToken {
  token: string;
  expiresAt: Date;
  createdAt: Date;
  isRevoked: boolean;
  deviceInfo?: string;
  ipAddress?: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  functionalCode?: string;
  phone?: string;
  refreshToken?: RefreshToken[];
  isActive: boolean;
  isEmailVerified: boolean;
  lastLoginAt?: Date;
  profilePicture?: string;
  bio?: string;
  isDeleted: boolean;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
  functionalCode?: string;
  phone?: string;
}

export interface UpdateUserDto {
  firstName?: string;
  lastName?: string;
  phone?: string;
  bio?: string;
  profilePicture?: string;
}

export interface UserFilters {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  page?: number;
  limit?: number;
}
