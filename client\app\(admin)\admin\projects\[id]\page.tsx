'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  FiArrowLeft, 
  FiEdit3, 
  FiTrash2, 
  FiUsers, 
  FiCalendar, 
  FiTarget,
  FiGlobe,
  FiGithub,
  FiExternalLink,
  FiStar,
  FiClock,
  FiDollarSign,
  FiTag,
  FiFolder,
  FiActivity,
  FiTrendingUp,
  FiCheckCircle,
  FiAlertCircle,
  FiPhone,
  FiMail
} from 'react-icons/fi';
import { useProject, useDeleteProject, useToggleProjectStatus } from '@/lib/hooks/use-projects';
import { useEmployees } from '@/lib/hooks/use-employees';
import { useTechnologies } from '@/lib/hooks/use-technologies';
import { ProjectStatus, ProjectPriority } from '@/lib/types/project';
import { Button } from '@/components/shared/forms/Button';
import { Alert } from '@/components/shared/Alert';
import { Spinner } from '@/components/shared/Spinner';
import { GlassCard } from '@/components/ui/glass-card';
import { Badge } from '@/components/shared/Badge';
import { Modal } from '@/components/shared/Modal';

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Hooks
  const { data: project, isLoading, error } = useProject(projectId);
  const { data: employeesData } = useEmployees({ limit: 1000 });
  const { data: technologiesData } = useTechnologies({ limit: 1000 });
  const deleteProjectMutation = useDeleteProject();
  const toggleStatusMutation = useToggleProjectStatus();

  const employees = employeesData?.employees || [];
  const technologies = technologiesData?.technologies || [];

  // Extract project data from API response
  const projectData = project?.data;

  // Handlers
  const handleDeleteProject = async () => {
    try {
      await deleteProjectMutation.mutateAsync(projectId);
      // Redirect will be handled by the mutation success callback
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleToggleStatus = async (action: 'activate' | 'deactivate' | 'feature' | 'unfeature') => {
    try {
      await toggleStatusMutation.mutateAsync({ id: projectId, action });
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Get status badge color
  const getStatusColor = (status: ProjectStatus) => {
    switch (status) {
      case ProjectStatus.PLANNING: return 'blue';
      case ProjectStatus.IN_PROGRESS: return 'green';
      case ProjectStatus.ON_HOLD: return 'yellow';
      case ProjectStatus.COMPLETED: return 'emerald';
      case ProjectStatus.CANCELLED: return 'red';
      case ProjectStatus.ARCHIVED: return 'gray';
      default: return 'gray';
    }
  };

  // Get priority badge color
  const getPriorityColor = (priority: ProjectPriority) => {
    switch (priority) {
      case ProjectPriority.LOW: return 'gray';
      case ProjectPriority.MEDIUM: return 'blue';
      case ProjectPriority.HIGH: return 'orange';
      case ProjectPriority.URGENT: return 'red';
      default: return 'gray';
    }
  };

  // Get project manager
  const projectManager = employees.find((emp: any) => emp.id === projectData?.projectManager);

  // Get team members
  const teamMembers = projectData?.members?.map((member: any) => ({
    ...member,
    employee: employees.find((emp: any) => emp.id === member.userId)
  })) || [];

  // Get project technologies
  const projectTechnologies = projectData?.technologies?.map((techId: any) =>
    technologies.find((tech: any) => tech._id === techId)
  ).filter(Boolean) || [];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !project || !projectData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
        <div className="max-w-7xl mx-auto">
          <Alert
            type="error"
            title="Error loading project"
            message={error instanceof Error ? error.message : 'Project not found'}
            actions={
              <Link href="/admin/projects">
                <Button variant="primary">Back to Projects</Button>
              </Link>
            }
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/projects">
              <Button
                variant="secondary"
                size="sm"
                className="flex items-center space-x-2"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span>Back to Projects</span>
              </Button>
            </Link>
            
            <div className="flex items-center space-x-3">
              {projectData?.mainImage ? (
                <img
                  src={projectData.mainImage}
                  alt={projectData.name}
                  className="w-12 h-12 rounded-xl object-cover"
                />
              ) : (
                <div className="p-3 bg-gradient-to-br from-primary-500 to-accent-purple rounded-xl">
                  <FiFolder className="w-6 h-6 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-3xl font-bold text-white">{projectData?.name}</h1>
                <p className="text-primary-300 mt-1">{projectData?.description}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              variant={projectData?.isFeatured ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => handleToggleStatus(projectData?.isFeatured ? 'unfeature' : 'feature')}
              disabled={toggleStatusMutation.isPending}
            >
              <FiStar className="w-4 h-4 mr-2" />
              {projectData?.isFeatured ? 'Featured' : 'Feature'}
            </Button>
            
            <Link href={`/admin/projects/${projectId}/edit`}>
              <Button variant="secondary" size="sm">
                <FiEdit3 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Link>
            
            <Button
              variant="danger"
              size="sm"
              onClick={() => setShowDeleteModal(true)}
            >
              <FiTrash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Status and Progress */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-primary-300 mb-1">Status</p>
                <Badge color={getStatusColor(projectData.status)} size="lg">
                  {projectData.status.replace('_', ' ')}
                </Badge>
              </div>
              <FiActivity className="w-8 h-8 text-primary-300" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-primary-300 mb-1">Priority</p>
                <Badge color={getPriorityColor(projectData.priority)} size="lg">
                  {projectData.priority}
                </Badge>
              </div>
              <FiTarget className="w-8 h-8 text-primary-300" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-primary-300 mb-1">Progress</p>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-dark-600 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-primary-500 to-accent-purple h-2 rounded-full transition-all duration-300"
                      style={{ width: `${projectData.progress}%` }}
                    />
                  </div>
                  <span className="text-white font-semibold">{projectData.progress}%</span>
                </div>
              </div>
              <FiTrendingUp className="w-8 h-8 text-primary-300" />
            </div>
          </GlassCard>

          <GlassCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-primary-300 mb-1">Team Size</p>
                <p className="text-2xl font-bold text-white">{projectData.members.length}</p>
              </div>
              <FiUsers className="w-8 h-8 text-primary-300" />
            </div>
          </GlassCard>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Project Details */}
            <GlassCard className="p-6">
              <h2 className="text-xl font-semibold text-white mb-6">Project Details</h2>
              
              <div className="space-y-6">
                {projectData.longDescription && (
                  <div>
                    <h3 className="text-lg font-medium text-white mb-3">Description</h3>
                    <p className="text-primary-300 leading-relaxed whitespace-pre-wrap">
                      {projectData.longDescription}
                    </p>
                  </div>
                )}

                {/* Timeline */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {projectData.startDate && (
                      <div className="flex items-center space-x-2">
                        <FiCalendar className="w-4 h-4 text-primary-300" />
                        <div>
                          <p className="text-sm text-primary-300">Start Date</p>
                          <p className="text-white">{new Date(projectData.startDate).toLocaleDateString()}</p>
                        </div>
                      </div>
                    )}
                    
                    {projectData.endDate && (
                      <div className="flex items-center space-x-2">
                        <FiCalendar className="w-4 h-4 text-primary-300" />
                        <div>
                          <p className="text-sm text-primary-300">End Date</p>
                          <p className="text-white">{new Date(projectData.endDate).toLocaleDateString()}</p>
                        </div>
                      </div>
                    )}
                    
                    {projectData.deadline && (
                      <div className="flex items-center space-x-2">
                        <FiAlertCircle className="w-4 h-4 text-orange-400" />
                        <div>
                          <p className="text-sm text-primary-300">Deadline</p>
                          <p className="text-white">{new Date(projectData.deadline).toLocaleDateString()}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Budget */}
                {projectData.budget && (
                  <div>
                    <h3 className="text-lg font-medium text-white mb-3">Budget</h3>
                    <div className="flex items-center space-x-2">
                      <FiDollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-white text-lg font-semibold">
                        {projectData.currency} {projectData.budget.toLocaleString()}
                      </span>
                    </div>
                  </div>
                )}

                {/* Links */}
                <div>
                  <h3 className="text-lg font-medium text-white mb-3">Links</h3>
                  <div className="flex flex-wrap gap-3">
                    {projectData.repositoryUrl && (
                      <a
                        href={projectData.repositoryUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 px-3 py-2 bg-dark-700 rounded-lg hover:bg-dark-600 transition-colors"
                      >
                        <FiGithub className="w-4 h-4 text-primary-300" />
                        <span className="text-white">Repository</span>
                        <FiExternalLink className="w-3 h-3 text-primary-300" />
                      </a>
                    )}
                    
                    {projectData.liveUrl && (
                      <a
                        href={projectData.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 px-3 py-2 bg-dark-700 rounded-lg hover:bg-dark-600 transition-colors"
                      >
                        <FiGlobe className="w-4 h-4 text-primary-300" />
                        <span className="text-white">Live Site</span>
                        <FiExternalLink className="w-3 h-3 text-primary-300" />
                      </a>
                    )}
                    
                    {projectData.demoUrl && (
                      <a
                        href={projectData.demoUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 px-3 py-2 bg-dark-700 rounded-lg hover:bg-dark-600 transition-colors"
                      >
                        <FiExternalLink className="w-4 h-4 text-primary-300" />
                        <span className="text-white">Demo</span>
                        <FiExternalLink className="w-3 h-3 text-primary-300" />
                      </a>
                    )}
                  </div>
                </div>

                {/* Tags */}
                {projectData.tags && projectData.tags.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-white mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {projectData.tags.map(tag => (
                        <div key={tag} className="flex items-center space-x-1 px-3 py-1 bg-primary-500/20 rounded-full">
                          <FiTag className="w-3 h-3 text-primary-300" />
                          <span className="text-sm text-primary-300">{tag}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </GlassCard>

            {/* Technologies */}
            {projectTechnologies.length > 0 && (
              <GlassCard className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6">Technologies</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {projectTechnologies.map((technology, index) => technology && (
                    <div key={technology._id || index} className="flex items-center space-x-3 p-3 bg-dark-700 rounded-lg">
                      {technology.icon && (
                        <img src={technology.icon} alt={technology.name} className="w-8 h-8" />
                      )}
                      <div>
                        <p className="text-white font-medium">{technology.name}</p>
                        <p className="text-xs text-primary-300">{technology.category}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            )}
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Project Manager */}
            {projectManager && (
              <GlassCard className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6">Project Manager</h2>
                <div className="flex items-center space-x-3">
                  {projectManager.profileImage ? (
                    <img
                      src={projectManager.profileImage}
                      alt={`${projectManager.firstName} ${projectManager.lastName}`}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary-500 to-accent-purple flex items-center justify-center text-white font-semibold">
                      {projectManager.firstName[0]}{projectManager.lastName[0]}
                    </div>
                  )}
                  <div>
                    <p className="text-white font-semibold">
                      {projectManager.firstName} {projectManager.lastName}
                    </p>
                    <p className="text-sm text-primary-300">{projectManager.position}</p>
                    <p className="text-sm text-primary-300">{projectManager.department}</p>
                  </div>
                </div>
              </GlassCard>
            )}

            {/* Team Members */}
            {teamMembers.length > 0 && (
              <GlassCard className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6">Team Members</h2>
                <div className="space-y-3">
                  {teamMembers.map(member => {
                    if (!member.employee) return null;
                    
                    return (
                      <div key={member.userId} className="flex items-center justify-between p-3 bg-dark-700 rounded-lg">
                        <div className="flex items-center space-x-3">
                          {member.employee.profileImage ? (
                            <img
                              src={member.employee.profileImage}
                              alt={`${member.employee.firstName} ${member.employee.lastName}`}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-500 to-accent-purple flex items-center justify-center text-white text-sm font-semibold">
                              {member.employee.firstName[0]}{member.employee.lastName[0]}
                            </div>
                          )}
                          <div>
                            <p className="text-white font-medium">
                              {member.employee.firstName} {member.employee.lastName}
                            </p>
                            <p className="text-xs text-primary-300">{member.employee.position}</p>
                          </div>
                        </div>
                        <Badge color="blue" size="sm">
                          {member.role}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              </GlassCard>
            )}

            {/* Client Information */}
            {(projectData.clientName || projectData.clientEmail || projectData.clientPhone) && (
              <GlassCard className="p-6">
                <h2 className="text-xl font-semibold text-white mb-6">Client Information</h2>
                <div className="space-y-3">
                  {projectData.clientName && (
                    <div>
                      <p className="text-sm text-primary-300">Client Name</p>
                      <p className="text-white font-medium">{projectData.clientName}</p>
                    </div>
                  )}
                  
                  {projectData.clientEmail && (
                    <div className="flex items-center space-x-2">
                      <FiMail className="w-4 h-4 text-primary-300" />
                      <a
                        href={`mailto:${projectData.clientEmail}`}
                        className="text-primary-400 hover:text-primary-300 transition-colors"
                      >
                        {projectData.clientEmail}
                      </a>
                    </div>
                  )}
                  
                  {projectData.clientPhone && (
                    <div className="flex items-center space-x-2">
                      <FiPhone className="w-4 h-4 text-primary-300" />
                      <a
                        href={`tel:${projectData.clientPhone}`}
                        className="text-primary-400 hover:text-primary-300 transition-colors"
                      >
                        {projectData.clientPhone}
                      </a>
                    </div>
                  )}
                </div>
              </GlassCard>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Delete Project"
          size="md"
        >
          <div className="space-y-4">
            <p className="text-primary-300">
              Are you sure you want to delete "{projectData.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteProject}
                disabled={deleteProjectMutation.isPending}
              >
                {deleteProjectMutation.isPending ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
}
