# Employee Code System

## Overview
This module manages employee codes, which are central to the authentication and user creation flow. Each employee code carries role permissions and tracks whether it has been used in registration.

## Features
- Admin can:
  - Create new employee codes with a role and description.
  - View all employee codes.
  - Edit an existing code (name, description, role).
  - Delete codes.
- Codes are **unique**, can be **used only once**, and define the **user's role**.

## Fields
- Code (string)
- Description (string)
- Role (enum: `ADMIN`, `EMPLOYEE`, `TEAM_LEADER`, `PROJECT_MANAGER`)
- isUsed (boolean)
- createdAt, updatedAt

## Usage in Other Systems
- **Authentication System**:
  - During signup, code must be validated and checked.
  - After successful registration, `isUsed = true`.
- **Employee Management System**:
  - When user is created, link the code to their record.
  - If user is deleted, the code is removed from the DB.

## Admin Interface
- Part of the Admin Panel.
- Filter/search by code or role.
- Button to mark a code as manually expired (optional).

## Technologies
- MongoDB for storage.
- Next.js (API routes or backend integration).
- Admin interface styled with TailwindCSS v4.

---

**Next**: `EMPLOYEE-MANAGEMENT.md`

