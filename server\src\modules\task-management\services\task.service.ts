import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { TaskRepository } from '../database/repositories/task.repository';
import { CreateTaskDto, UpdateTaskDto, AssignTaskDto, TaskFilterDto } from '../dto/task.dto';
import { Task, TaskDocument } from '../database/schemas/task.schema';
import { TaskError } from './task.error';
import { UserService } from '../../user-management/services/user.service';
import { ProjectService } from '../../project-management/services/project.service';
import { TechnologyService } from '../../technology-management/services/technology.service';
import { Types } from 'mongoose';

@Injectable()
export class TaskService {
  constructor(
    private readonly taskRepository: TaskRepository,
    private readonly taskError: TaskError,
    private readonly userService: UserService,
    private readonly projectService: ProjectService,
    private readonly technologyService: TechnologyService,
  ) {}

  async create(createTaskDto: CreateTaskDto, createdBy: string): Promise<TaskDocument> {
    try {
      // Validate project exists
      if (createTaskDto.projectId) {
        await this.projectService.findById(createTaskDto.projectId);
      }

      // Validate assigned user exists
      if (createTaskDto.assignedTo) {
        await this.userService.findById(createTaskDto.assignedTo);
      }

      // Validate technologies exist
      if (createTaskDto.technologies && createTaskDto.technologies.length > 0) {
        for (const techId of createTaskDto.technologies) {
          await this.technologyService.findById(techId);
        }
      }

      const taskData = {
        ...createTaskDto,
        project: createTaskDto.projectId,
        createdBy,
        status: createTaskDto.status || 'TODO',
        priority: createTaskDto.priority || 'MEDIUM',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Remove projectId since we're using project
      delete taskData.projectId;

      return await this.taskRepository.create(taskData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to create task');
    }
  }

  async findAll(filters: TaskFilterDto = {}): Promise<TaskDocument[]> {
    try {
      return await this.taskRepository.findAll(filters);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to fetch tasks');
    }
  }

  async findById(id: string): Promise<TaskDocument> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new BadRequestException('Invalid task ID format');
      }

      const task = await this.taskRepository.findById(id);
      if (!task) {
        throw new NotFoundException(`Task with ID ${id} not found`);
      }

      return task;
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to fetch task');
    }
  }

  async findByProject(projectId: string): Promise<TaskDocument[]> {
    try {
      if (!Types.ObjectId.isValid(projectId)) {
        throw new BadRequestException('Invalid project ID format');
      }

      return await this.taskRepository.findByProject(projectId);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to fetch project tasks');
    }
  }

  async findByAssignee(assigneeId: string): Promise<TaskDocument[]> {
    try {
      if (!Types.ObjectId.isValid(assigneeId)) {
        throw new BadRequestException('Invalid assignee ID format');
      }

      return await this.taskRepository.findByAssignee(assigneeId);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to fetch assigned tasks');
    }
  }

  async update(id: string, updateTaskDto: UpdateTaskDto, updatedBy: string): Promise<TaskDocument> {
    try {
      const existingTask = await this.findById(id);

      // Validate project exists if being updated
      if (updateTaskDto.projectId && updateTaskDto.projectId !== existingTask.project?.toString()) {
        await this.projectService.findById(updateTaskDto.projectId);
      }

      // Validate assigned user exists if being updated
      if (updateTaskDto.assignedTo && updateTaskDto.assignedTo !== existingTask.assignedTo?.toString()) {
        await this.userService.findById(updateTaskDto.assignedTo);
      }

      // Validate technologies exist if being updated
      if (updateTaskDto.technologies && updateTaskDto.technologies.length > 0) {
        for (const techId of updateTaskDto.technologies) {
          await this.technologyService.findById(techId);
        }
      }

      const updateData = {
        ...updateTaskDto,
        updatedBy,
        updatedAt: new Date(),
      };

      return await this.taskRepository.update(id, updateData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to update task');
    }
  }

  async assign(id: string, assignTaskDto: AssignTaskDto, assignedBy: string): Promise<TaskDocument> {
    try {
      const task = await this.findById(id);

      // Validate assigned user exists
      await this.userService.findById(assignTaskDto.assignedTo);

      const updateData = {
        assignedTo: assignTaskDto.assignedTo,
        assignedBy,
        assignedAt: new Date(),
        updatedAt: new Date(),
      };

      return await this.taskRepository.update(id, updateData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to assign task');
    }
  }

  async updateStatus(id: string, status: string, updatedBy: string): Promise<TaskDocument> {
    try {
      const validStatuses = ['TODO', 'IN_PROGRESS', 'IN_REVIEW', 'COMPLETED', 'CANCELLED'];
      if (!validStatuses.includes(status)) {
        throw new BadRequestException(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
      }

      const updateData = {
        status,
        updatedBy,
        updatedAt: new Date(),
      };

      // If marking as completed, set completion date
      if (status === 'COMPLETED') {
        updateData['completedAt'] = new Date();
      }

      return await this.taskRepository.update(id, updateData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to update task status');
    }
  }

  async addAttachment(id: string, filename: string, originalName: string, size: number, uploadedBy: string): Promise<TaskDocument> {
    try {
      const task = await this.findById(id);

      const attachment = {
        filename,
        originalName,
        size,
        uploadedBy,
        uploadedAt: new Date(),
      };

      const updateData = {
        $push: { attachments: attachment },
        updatedAt: new Date(),
      };

      return await this.taskRepository.update(id, updateData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to add attachment');
    }
  }

  async removeAttachment(id: string, attachmentId: string, removedBy: string): Promise<TaskDocument> {
    try {
      const task = await this.findById(id);

      const updateData = {
        $pull: { attachments: { _id: attachmentId } },
        updatedBy: removedBy,
        updatedAt: new Date(),
      };

      return await this.taskRepository.update(id, updateData);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to remove attachment');
    }
  }

  async delete(id: string, deletedBy: string): Promise<void> {
    try {
      const task = await this.findById(id);

      await this.taskRepository.softDelete(id, deletedBy);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to delete task');
    }
  }

  async getTaskStatistics(projectId?: string): Promise<any> {
    try {
      return await this.taskRepository.getStatistics(projectId);
    } catch (error) {
      throw this.taskError.handleError(error, 'Failed to get task statistics');
    }
  }
}
