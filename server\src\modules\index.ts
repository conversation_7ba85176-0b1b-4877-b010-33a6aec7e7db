// New modular system imports
import { AuthSystemModule } from "./auth-system/auth-system.module";
import { UserManagementModule } from "./user-management/user-management.module";
import { EmployeeManagementModule } from "./employee-management/employee-management.module";
import { EmployeeCodeModule } from "./employee-code/employee-code.module";
import { ProjectManagementModule } from "./project-management/project-management.module";
import { TaskManagementModule } from "./task-management/task-management.module";
import { TechnologyManagementModule } from "./technology-management/technology-management.module";
import { ServiceManagementModule } from "./service-management/service-management.module";
import { HealthMonitoringModule } from "./health-monitoring/health-monitoring.module";

export const Modules = [
  // Core system modules
  UserManagementModule,
  AuthSystemModule,

  // Business domain modules
  EmployeeManagementModule,
  EmployeeCodeModule,
  ProjectManagementModule,
  TaskManagementModule,
  TechnologyManagementModule,
  ServiceManagementModule,

  // System monitoring
  HealthMonitoringModule
];

// Export individual modules for direct imports
export {
  AuthSystemModule,
  UserManagementModule,
  EmployeeManagementModule,
  EmployeeCodeModule,
  ProjectManagementModule,
  TaskManagementModule,
  TechnologyManagementModule,
  ServiceManagementModule,
  HealthMonitoringModule
};
