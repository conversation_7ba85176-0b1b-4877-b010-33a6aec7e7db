{"name": "sillalink-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@swc/helpers": "^0.5.17", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.75.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.0.0", "framer-motion": "^12.6.5", "lottie-react": "^2.4.1", "lottie-web": "^5.12.2", "lucide-react": "^0.503.0", "next": "15.3.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-spinners": "^0.17.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}