import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TaskDocument = Task & Document;

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  IN_REVIEW = 'IN_REVIEW',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum TaskType {
  FEATURE = 'FEATURE',
  BUG = 'BUG',
  IMPROVEMENT = 'IMPROVEMENT',
  RESEARCH = 'RESEARCH',
  DOCUMENTATION = 'DOCUMENTATION',
  TESTING = 'TESTING',
  MAINTENANCE = 'MAINTENANCE'
}

@Schema({ timestamps: true })
export class TaskComment {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  author: Types.ObjectId;

  @Prop({ required: true, trim: true, maxlength: 2000 })
  content: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ default: false })
  isEdited: boolean;
}

@Schema({ timestamps: true })
export class TaskAttachment {
  @Prop({ required: true, trim: true })
  filename: string;

  @Prop({ required: true, trim: true })
  originalName: string;

  @Prop({ required: true, trim: true })
  mimeType: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: true, trim: true })
  url: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  uploadedBy: Types.ObjectId;

  @Prop({ type: Date, default: Date.now })
  uploadedAt: Date;
}

@Schema({ timestamps: true })
export class TaskTimeLog {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  user: Types.ObjectId;

  @Prop({ required: true, trim: true, maxlength: 500 })
  description: string;

  @Prop({ required: true, min: 0 })
  hoursSpent: number;

  @Prop({ type: Date, required: true })
  logDate: Date;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

@Schema({ timestamps: true, collection: 'tasks' })
export class Task {
  @Prop({ required: true, trim: true, minlength: 3, maxlength: 200 })
  title: string;

  @Prop({ trim: true, maxlength: 2000 })
  description?: string;

  @Prop({ type: String, enum: TaskStatus, default: TaskStatus.TODO })
  status: TaskStatus;

  @Prop({ type: String, enum: TaskPriority, default: TaskPriority.MEDIUM })
  priority: TaskPriority;

  @Prop({ type: String, enum: TaskType, default: TaskType.FEATURE })
  type: TaskType;

  @Prop({ type: Types.ObjectId, ref: 'Project', required: true })
  project: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  assignedTo?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User' })
  reviewer?: Types.ObjectId;

  @Prop({ type: Date })
  dueDate?: Date;

  @Prop({ type: Date })
  startDate?: Date;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Number, min: 0, max: 100, default: 0 })
  progress: number;

  @Prop({ type: Number, min: 0 })
  estimatedHours?: number;

  @Prop({ type: Number, min: 0, default: 0 })
  actualHours: number;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: [Types.ObjectId], ref: 'Technology', default: [] })
  technologies: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Task', default: [] })
  dependencies: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Task', default: [] })
  subtasks: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'Task' })
  parentTask?: Types.ObjectId;

  @Prop({ type: [TaskComment], default: [] })
  comments: TaskComment[];

  @Prop({ type: [TaskAttachment], default: [] })
  attachments: TaskAttachment[];

  @Prop({ type: [TaskTimeLog], default: [] })
  timeLogs: TaskTimeLog[];

  @Prop({ type: String, trim: true })
  repositoryUrl?: string;

  @Prop({ type: String, trim: true })
  branchName?: string;

  @Prop({ type: String, trim: true })
  pullRequestUrl?: string;

  @Prop({ type: String, trim: true })
  testingNotes?: string;

  @Prop({ type: String, trim: true })
  acceptanceCriteria?: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  isDeleted: boolean;

  @Prop({ type: Date, default: null })
  deletedAt?: Date;

  @Prop({ type: String, trim: true, maxlength: 1000 })
  notes?: string;
}

export const TaskSchema = SchemaFactory.createForClass(Task);
export const TaskCommentSchema = SchemaFactory.createForClass(TaskComment);
export const TaskAttachmentSchema = SchemaFactory.createForClass(TaskAttachment);
export const TaskTimeLogSchema = SchemaFactory.createForClass(TaskTimeLog);

// Indexes for better performance
TaskSchema.index({ project: 1, status: 1 });
TaskSchema.index({ assignedTo: 1, status: 1 });
TaskSchema.index({ createdBy: 1 });
TaskSchema.index({ dueDate: 1, status: 1 });
TaskSchema.index({ priority: 1, status: 1 });
TaskSchema.index({ type: 1, project: 1 });
TaskSchema.index({ isDeleted: 1, deletedAt: 1 });
TaskSchema.index({ tags: 1 });
TaskSchema.index({ parentTask: 1 });
TaskSchema.index({ title: 'text', description: 'text' });
