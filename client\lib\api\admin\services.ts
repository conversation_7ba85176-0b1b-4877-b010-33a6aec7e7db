import apiClient from '../client';
import { Service, CreateServiceDto, UpdateServiceDto, ServiceFilters, ServiceInquiry, CreateInquiryDto } from '@/lib/types/service';
import { ApiResponse, PaginatedResponse } from '@/lib/types/api';

export const adminServicesApi = {
  // Get all services with filters and pagination
  getAll: (params: ServiceFilters): Promise<ApiResponse<PaginatedResponse<Service>>> =>
    apiClient.get('/admin/services', { params }),

  // Get service by ID
  getById: (id: string): Promise<ApiResponse<Service>> =>
    apiClient.get(`/admin/services/${id}`),

  // Create new service
  create: (data: CreateServiceDto): Promise<ApiResponse<Service>> =>
    apiClient.post('/admin/services', data),

  // Update service
  update: (id: string, data: UpdateServiceDto): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}`, data),

  // Delete service (soft delete)
  delete: (id: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/services/${id}`),

  // Bulk operations
  bulkDelete: (ids: string[]): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/services/bulk-delete', { ids }),

  bulkUpdateStatus: (ids: string[], status: string): Promise<ApiResponse<void>> =>
    apiClient.post('/admin/services/bulk-update-status', { ids, status }),

  // Service status management
  activate: (id: string): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}/activate`),

  deactivate: (id: string): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}/deactivate`),

  feature: (id: string): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}/feature`),

  unfeature: (id: string): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}/unfeature`),

  // Service images
  uploadImage: (serviceId: string, file: File): Promise<ApiResponse<{ imageUrl: string }>> => {
    const formData = new FormData();
    formData.append('image', file);
    return apiClient.post(`/admin/services/${serviceId}/upload-image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  deleteImage: (serviceId: string, imageUrl: string): Promise<ApiResponse<void>> =>
    apiClient.delete(`/admin/services/${serviceId}/images`, { data: { imageUrl } }),

  setMainImage: (serviceId: string, imageUrl: string): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${serviceId}/main-image`, { imageUrl }),

  // Service packages management
  addPackage: (serviceId: string, packageData: any): Promise<ApiResponse<Service>> =>
    apiClient.post(`/admin/services/${serviceId}/packages`, packageData),

  updatePackage: (serviceId: string, packageId: string, packageData: any): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${serviceId}/packages/${packageId}`, packageData),

  deletePackage: (serviceId: string, packageId: string): Promise<ApiResponse<Service>> =>
    apiClient.delete(`/admin/services/${serviceId}/packages/${packageId}`),

  // Service features management
  addFeature: (serviceId: string, featureData: any): Promise<ApiResponse<Service>> =>
    apiClient.post(`/admin/services/${serviceId}/features`, featureData),

  updateFeature: (serviceId: string, featureId: string, featureData: any): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${serviceId}/features/${featureId}`, featureData),

  deleteFeature: (serviceId: string, featureId: string): Promise<ApiResponse<Service>> =>
    apiClient.delete(`/admin/services/${serviceId}/features/${featureId}`),

  // Service technologies
  addTechnology: (serviceId: string, technologyId: string): Promise<ApiResponse<Service>> =>
    apiClient.post(`/admin/services/${serviceId}/technologies`, { technologyId }),

  removeTechnology: (serviceId: string, technologyId: string): Promise<ApiResponse<Service>> =>
    apiClient.delete(`/admin/services/${serviceId}/technologies/${technologyId}`),

  // Service team members
  addTeamMember: (serviceId: string, memberId: string): Promise<ApiResponse<Service>> =>
    apiClient.post(`/admin/services/${serviceId}/team-members`, { memberId }),

  removeTeamMember: (serviceId: string, memberId: string): Promise<ApiResponse<Service>> =>
    apiClient.delete(`/admin/services/${serviceId}/team-members/${memberId}`),

  // Service portfolio projects
  addPortfolioProject: (serviceId: string, projectId: string): Promise<ApiResponse<Service>> =>
    apiClient.post(`/admin/services/${serviceId}/portfolio-projects`, { projectId }),

  removePortfolioProject: (serviceId: string, projectId: string): Promise<ApiResponse<Service>> =>
    apiClient.delete(`/admin/services/${serviceId}/portfolio-projects/${projectId}`),

  // Service inquiries management
  getInquiries: (serviceId?: string): Promise<ApiResponse<PaginatedResponse<ServiceInquiry>>> =>
    apiClient.get('/admin/services/inquiries', { params: { serviceId } }),

  getInquiryById: (id: string): Promise<ApiResponse<ServiceInquiry>> =>
    apiClient.get(`/admin/services/inquiries/${id}`),

  updateInquiryStatus: (id: string, status: string): Promise<ApiResponse<ServiceInquiry>> =>
    apiClient.patch(`/admin/services/inquiries/${id}/status`, { status }),

  assignInquiry: (id: string, assignedTo: string): Promise<ApiResponse<ServiceInquiry>> =>
    apiClient.patch(`/admin/services/inquiries/${id}/assign`, { assignedTo }),

  addInquiryNote: (id: string, note: string): Promise<ApiResponse<ServiceInquiry>> =>
    apiClient.post(`/admin/services/inquiries/${id}/notes`, { note }),

  // Service analytics
  getStats: (): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/services/stats'),

  getServiceAnalytics: (id: string): Promise<ApiResponse<any>> =>
    apiClient.get(`/admin/services/${id}/analytics`),

  getInquiryStats: (): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/services/inquiry-stats'),

  // Service reports
  getPerformanceReport: (serviceId?: string, period?: string): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/services/performance-report', { 
      params: { serviceId, period }
    }),

  getRevenueReport: (period?: string): Promise<ApiResponse<any>> =>
    apiClient.get('/admin/services/revenue-report', { 
      params: { period }
    }),

  // Service export
  export: (format: 'csv' | 'excel', filters?: ServiceFilters): Promise<Blob> =>
    apiClient.get('/admin/services/export', { 
      params: { format, ...filters },
      responseType: 'blob'
    }),

  exportInquiries: (format: 'csv' | 'excel', filters?: any): Promise<Blob> =>
    apiClient.get('/admin/services/inquiries/export', { 
      params: { format, ...filters },
      responseType: 'blob'
    }),

  // Service templates
  getTemplates: (): Promise<ApiResponse<any[]>> =>
    apiClient.get('/admin/services/templates'),

  createFromTemplate: (templateId: string, data: any): Promise<ApiResponse<Service>> =>
    apiClient.post('/admin/services/from-template', { templateId, ...data }),

  // Service SEO and marketing
  updateSEO: (id: string, seoData: any): Promise<ApiResponse<Service>> =>
    apiClient.patch(`/admin/services/${id}/seo`, seoData),

  generateDescription: (id: string): Promise<ApiResponse<{ description: string }>> =>
    apiClient.post(`/admin/services/${id}/generate-description`),
};
