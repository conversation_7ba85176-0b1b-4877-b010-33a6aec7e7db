# Authentication System

## Overview
The authentication system is a multi-step signup and login flow with strong validation, OTP email verification, and role-based routing. This system is deeply integrated with the **Employee Code System**, from which it derives the user role and eligibility for registration.

## Signup Flow

### Step 1: Enter Employee Code
- User enters an employee code.
- System checks if the code:
  - Exists in the database.
  - Is not already used.
- If valid, proceed to Step 2.
- If invalid or used, show an appropriate error message.

### Step 2: Enter Personal Info
- Fields: First Name, Last Name, Email, Phone Number.
- Upon submission:
  - Validate email and phone.
  - Send a 6-digit OTP to the entered email.
  - Move to Step 3.

### Step 3: Enter OTP
- User enters the OTP.
- Validate against the one stored (with expiration).
- If correct, proceed to Step 4.
- If expired or incorrect, show an error message.

### Step 4: Set Password
- User sets and confirms password.
- System validates password strength.
- Upon success:
  - Create the user account.
  - Link the employee code and mark it as used.
  - Assign role based on the code's role.
  - Redirect based on role:
    - EMPLOYEE / TEAM_LEADER / PROJECT_MANAGER → Profile Page.
    - ADMIN → Admin Profile Page with access to Control Panel.

## Login Flow
- Standard email + password login.
- JWT-based session management.
- Protected routes depending on user role.

## Notes
- Uses TailwindCSS v4 for frontend design.
- OTP has a limited lifetime and is stored temporarily.
- Backend must validate email existence and OTP integrity.

## Integration with Other Systems
- **Employee Code System**: Validates and fetches role from the code.
- **Employee Management System**: Creates an employee profile upon successful signup.
- **Admin Panel**: Admins redirected to dashboard access.

## Error Handling
- Handles edge cases such as reused codes, expired OTPs, invalid inputs, and weak passwords.

## Technologies
- Next.js 15
- TypeScript
- TailwindCSS v4
- MongoDB
- Redis (optional for OTP expiry)

---

**Next**: `EMPLOYEE-CODE.md`

