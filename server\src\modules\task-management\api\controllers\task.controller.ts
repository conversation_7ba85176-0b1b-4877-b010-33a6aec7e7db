import { <PERSON><PERSON><PERSON><PERSON> } from '@Modules/user-management';
import { JwtAuthGuard } from '@Package/auth/guards';
import {
  Controller,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { TaskService } from '../../services/task.service';
import { UpdateTaskDto, TaskFilterDto } from './../../dto/task.dto';

@Controller('tasks')
@UseGuards(JwtAuthGuard)
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Get('my-tasks')
  async getMyTasks(@Request() req, @Query() filters: TaskFilterDto) {
    try {
      // Filter tasks assigned to the current user
      const userFilters = {
        ...filters,
        assignedTo: req.user.id,
      };

      const tasks = await this.taskService.findAll(userFilters);
      return {
        success: true,
        message: 'My tasks retrieved successfully',
        data: tasks,
        count: tasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('my-created')
  async getMyCreatedTasks(@Request() req, @Query() filters: TaskFilterDto) {
    try {
      // Filter tasks created by the current user
      const userFilters = {
        ...filters,
        createdBy: req.user.id,
      };

      const tasks = await this.taskService.findAll(userFilters);
      return {
        success: true,
        message: 'My created tasks retrieved successfully',
        data: tasks,
        count: tasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('project/:projectId')
  async getProjectTasks(@Param('projectId') projectId: string, @Request() req) {
    try {
      const tasks = await this.taskService.findByProject(projectId);
      
      // Filter tasks based on user role and permissions
      let filteredTasks = tasks;
      
      if (req.user.role === UserRole.EMPLOYEE) {
        // Employees can only see tasks assigned to them or created by them
        filteredTasks = tasks.filter(task => 
          task.assignedTo?.toString() === req.user.id || 
          task.createdBy?.toString() === req.user.id
        );
      }

      return {
        success: true,
        message: 'Project tasks retrieved successfully',
        data: filteredTasks,
        count: filteredTasks.length,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const task = await this.taskService.findById(id);

      // Check if user has permission to view this task
      const canView =
        req.user.role === UserRole.ADMIN ||
        task.assignedTo?.toString() === req.user.id ||
        task.createdBy?.toString() === req.user.id;

      if (!canView) {
        throw new ForbiddenException('You do not have permission to view this task');
      }

      return {
        success: true,
        message: 'Task retrieved successfully',
        data: task,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id/status')
  async updateMyTaskStatus(
    @Param('id') id: string,
    @Body('status') status: string,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.findById(id);

      // Check if user can update this task status
      const canUpdate =
        req.user.role === UserRole.ADMIN ||
        task.assignedTo?.toString() === req.user.id;

      if (!canUpdate) {
        throw new ForbiddenException('You do not have permission to update this task status');
      }

      const updatedTask = await this.taskService.updateStatus(id, status, req.user.id);
      return {
        success: true,
        message: 'Task status updated successfully',
        data: updatedTask,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id')
  async updateMyTask(
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
    @Request() req,
  ) {
    try {
      const task = await this.taskService.findById(id);

      // Check if user can update this task
      const canUpdate =
        req.user.role === UserRole.ADMIN ||
        task.assignedTo?.toString() === req.user.id ||
        task.createdBy?.toString() === req.user.id;

      if (!canUpdate) {
        throw new ForbiddenException('You do not have permission to update this task');
      }

      // Employees can only update certain fields
      if (req.user.role === UserRole.EMPLOYEE) {
        const allowedFields = ['description', 'status'];
        const filteredUpdate = {};
        
        allowedFields.forEach(field => {
          if (updateTaskDto[field] !== undefined) {
            filteredUpdate[field] = updateTaskDto[field];
          }
        });

        updateTaskDto = filteredUpdate as UpdateTaskDto;
      }

      const updatedTask = await this.taskService.update(id, updateTaskDto, req.user.id);
      return {
        success: true,
        message: 'Task updated successfully',
        data: updatedTask,
      };
    } catch (error) {
      throw error;
    }
  }

  @Put(':id/attachments')
  @UseInterceptors(FileInterceptor('file'))
  async addAttachment(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
  ) {
    try {
      if (!file) {
        throw new BadRequestException('No file uploaded');
      }

      const task = await this.taskService.findById(id);

      // Check if user can add attachments to this task
      const canAddAttachment =
        req.user.role === UserRole.ADMIN ||
        task.assignedTo?.toString() === req.user.id ||
        task.createdBy?.toString() === req.user.id;

      if (!canAddAttachment) {
        throw new ForbiddenException('You do not have permission to add attachments to this task');
      }

      const updatedTask = await this.taskService.addAttachment(
        id,
        file.filename,
        file.originalname,
        file.size,
        req.user.id,
      );

      return {
        success: true,
        message: 'Attachment added successfully',
        data: updatedTask,
      };
    } catch (error) {
      throw error;
    }
  }
}
